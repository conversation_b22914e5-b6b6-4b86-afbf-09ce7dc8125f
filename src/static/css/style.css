/* Custom styles for Multi Atendimento */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
}

/* Layout */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Chat Interface */
.chat-container {
    height: 70vh;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #fff;
}

.message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    max-width: 70%;
}

.message.incoming {
    background-color: #e9ecef;
    margin-right: auto;
}

.message.outgoing {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 0.25rem;
}

.message-status {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.message-status i {
    margin-right: 0.25rem;
}

/* Attendance List */
.attendance-item {
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.attendance-item:hover {
    background-color: #f8f9fa;
}

.attendance-item.priority-high {
    border-left: 4px solid var(--warning-color);
}

.attendance-item.priority-urgent {
    border-left: 4px solid var(--danger-color);
}

.attendance-item.unread {
    background-color: #fff3cd;
}

/* Status Badges */
.status-online {
    color: var(--success-color);
}

.status-offline {
    color: var(--secondary-color);
}

.status-away {
    color: var(--warning-color);
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    font-style: italic;
    color: var(--secondary-color);
}

.typing-dots {
    display: inline-flex;
    margin-left: 0.5rem;
}

.typing-dots span {
    height: 6px;
    width: 6px;
    background-color: var(--secondary-color);
    border-radius: 50%;
    display: inline-block;
    margin: 0 1px;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Notifications */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--danger-color);
    color: white;
    border-radius: 50%;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    min-width: 1.5rem;
    text-align: center;
}

/* Quick Actions */
.quick-actions {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.quick-actions .btn {
    margin-bottom: 0.5rem;
    border-radius: 50%;
    width: 3.5rem;
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Statistics */
.stat-card {
    text-align: center;
    padding: 1.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    opacity: 0.8;
}

/* Forms */
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Media Queries */
@media (max-width: 768px) {
    .chat-container {
        height: 50vh;
    }
    
    .message {
        max-width: 85%;
    }
    
    .quick-actions {
        bottom: 1rem;
        right: 1rem;
    }
    
    .quick-actions .btn {
        width: 3rem;
        height: 3rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #212529;
        color: #fff;
    }
    
    .card {
        background-color: #343a40;
        border-color: #495057;
    }
    
    .chat-container {
        background-color: #343a40;
        border-color: #495057;
    }
    
    .message.incoming {
        background-color: #495057;
        color: #fff;
    }
}

/* Loading Spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.spinner-border-lg {
    width: 3rem;
    height: 3rem;
}

/* Custom Scrollbar */
.chat-container::-webkit-scrollbar {
    width: 6px;
}

.chat-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.2s ease;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.border-start-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-start-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-start-warning {
    border-left: 4px solid var(--warning-color) !important;
}

.border-start-danger {
    border-left: 4px solid var(--danger-color) !important;
}
