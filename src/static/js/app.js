// Main JavaScript file for Multi Atendimento

// Global variables
let socket = null;
let currentAttendanceId = null;
let typingTimer = null;
let isTyping = false;

// Initialize application
$(document).ready(function() {
    initializeWebSocket();
    initializeNotifications();
    initializeEventHandlers();
    
    // Auto-refresh data every 30 seconds
    setInterval(refreshData, 30000);
});

// WebSocket initialization
function initializeWebSocket() {
    if (typeof io !== 'undefined') {
        socket = io();
        
        socket.on('connect', function() {
            console.log('Connected to WebSocket server');
            showToast('Conectado ao servidor', 'success');
        });
        
        socket.on('disconnect', function() {
            console.log('Disconnected from WebSocket server');
            showToast('Conexão perdida. Tentando reconectar...', 'warning');
        });
        
        socket.on('new_attendance_notification', function(data) {
            handleNewAttendanceNotification(data);
        });
        
        socket.on('new_message', function(data) {
            handleNewMessage(data);
        });
        
        socket.on('attendance_accepted', function(data) {
            handleAttendanceAccepted(data);
        });
        
        socket.on('attendance_closed', function(data) {
            handleAttendanceClosed(data);
        });
        
        socket.on('attendance_transferred', function(data) {
            handleAttendanceTransferred(data);
        });
        
        socket.on('typing_status', function(data) {
            handleTypingStatus(data);
        });
    }
}

// Notification system
function initializeNotifications() {
    // Request notification permission
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
    
    // Initialize notification sound
    window.notificationSound = new Audio('/static/sounds/notification.mp3');
}

// Event handlers
function initializeEventHandlers() {
    // Message form submission
    $('#message-form').on('submit', function(e) {
        e.preventDefault();
        sendMessage();
    });
    
    // Typing indicator
    $('#message-content').on('input', function() {
        handleTyping();
    });
    
    // File upload
    $('#media-file').on('change', function() {
        handleFileUpload(this);
    });
    
    // Quick reply buttons
    $('.quick-reply-btn').on('click', function() {
        const message = $(this).data('message');
        $('#message-content').val(message);
        $('#message-content').focus();
    });
    
    // Accept attendance buttons
    $('.accept-attendance-btn').on('click', function() {
        const attendanceId = $(this).data('attendance-id');
        acceptAttendance(attendanceId);
    });
    
    // Close attendance buttons
    $('.close-attendance-btn').on('click', function() {
        const attendanceId = $(this).data('attendance-id');
        showCloseAttendanceModal(attendanceId);
    });
    
    // Transfer attendance buttons
    $('.transfer-attendance-btn').on('click', function() {
        const attendanceId = $(this).data('attendance-id');
        showTransferAttendanceModal(attendanceId);
    });
    
    // Auto-resize textarea
    $('textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Scroll to bottom of chat
    scrollChatToBottom();
}

// WebSocket event handlers
function handleNewAttendanceNotification(data) {
    updateNotificationBadge();
    showBrowserNotification('Novo Atendimento', `Atendimento #${data.attendance_id} recebido`);
    playNotificationSound();
    
    // Update pending attendances list if on dashboard
    if (window.location.pathname.includes('dashboard')) {
        refreshPendingAttendances();
    }
}

function handleNewMessage(data) {
    if (currentAttendanceId && data.attendance_id == currentAttendanceId) {
        appendMessageToChat(data);
        scrollChatToBottom();
        
        // Mark as read if message is incoming
        if (data.direction === 'incoming') {
            markMessageAsRead(data.message_id);
        }
    } else {
        // Show notification for messages in other chats
        updateNotificationBadge();
        showBrowserNotification('Nova Mensagem', data.content.substring(0, 50) + '...');
        playNotificationSound();
    }
}

function handleAttendanceAccepted(data) {
    showToast(`Atendimento aceito por ${data.operator_name}`, 'info');
    
    // Remove from pending list if visible
    $(`.attendance-item[data-id="${data.attendance_id}"]`).fadeOut();
}

function handleAttendanceClosed(data) {
    showToast(`Atendimento #${data.attendance_id} finalizado`, 'success');
    
    // Redirect if currently viewing this attendance
    if (currentAttendanceId == data.attendance_id) {
        setTimeout(() => {
            window.location.href = '/chat';
        }, 2000);
    }
}

function handleAttendanceTransferred(data) {
    showToast(`Atendimento transferido para ${data.new_operator_name}`, 'info');
    
    // Redirect if this was our attendance
    if (currentAttendanceId == data.attendance_id) {
        setTimeout(() => {
            window.location.href = '/chat';
        }, 2000);
    }
}

function handleTypingStatus(data) {
    if (currentAttendanceId && data.room == `attendance_${currentAttendanceId}`) {
        if (data.is_typing) {
            showTypingIndicator(data.user_id);
        } else {
            hideTypingIndicator();
        }
    }
}

// Message functions
function sendMessage() {
    const content = $('#message-content').val().trim();
    const mediaType = $('#media-type').val();
    const mediaUrl = $('#media-url').val();
    
    if (!content && !mediaUrl) {
        showToast('Digite uma mensagem', 'warning');
        return;
    }
    
    const messageData = {
        attendance_id: currentAttendanceId,
        content: content,
        media_type: mediaType,
        media_url: mediaUrl
    };
    
    // Show loading state
    $('#send-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');
    
    $.ajax({
        url: '/chat/send_message',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(messageData),
        success: function(response) {
            if (response.success) {
                $('#message-content').val('');
                $('#media-url').val('');
                $('#media-type').val('text');
                
                // Message will be added via WebSocket
            } else {
                showToast('Erro ao enviar mensagem', 'error');
            }
        },
        error: function() {
            showToast('Erro ao enviar mensagem', 'error');
        },
        complete: function() {
            $('#send-btn').prop('disabled', false).html('<i class="fas fa-paper-plane"></i>');
            $('#message-content').focus();
        }
    });
}

function appendMessageToChat(messageData) {
    const messageHtml = createMessageHtml(messageData);
    $('#chat-messages').append(messageHtml);
}

function createMessageHtml(data) {
    const messageClass = data.direction === 'incoming' ? 'incoming' : 'outgoing';
    const timeStr = new Date(data.timestamp).toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    let mediaHtml = '';
    if (data.media_type === 'image') {
        mediaHtml = `<img src="${data.media_url}" class="img-fluid rounded mb-2" style="max-width: 200px;">`;
    } else if (data.media_type === 'file') {
        mediaHtml = `<a href="${data.media_url}" target="_blank" class="btn btn-sm btn-outline-primary mb-2">
            <i class="fas fa-download"></i> Download
        </a>`;
    }
    
    return `
        <div class="message ${messageClass} fade-in" data-id="${data.message_id || ''}">
            ${mediaHtml}
            <div class="message-content">${data.content}</div>
            <div class="message-time">${timeStr}</div>
            ${data.direction === 'outgoing' ? getMessageStatusHtml(data.status) : ''}
        </div>
    `;
}

function getMessageStatusHtml(status) {
    let icon = 'fas fa-clock';
    let color = 'text-muted';
    
    switch (status) {
        case 'sent':
            icon = 'fas fa-check';
            break;
        case 'delivered':
            icon = 'fas fa-check-double';
            color = 'text-info';
            break;
        case 'read':
            icon = 'fas fa-check-double';
            color = 'text-primary';
            break;
        case 'failed':
            icon = 'fas fa-exclamation-triangle';
            color = 'text-danger';
            break;
    }
    
    return `<div class="message-status ${color}"><i class="${icon}"></i></div>`;
}

// Typing indicator
function handleTyping() {
    if (!isTyping) {
        isTyping = true;
        socket.emit('typing', {
            room: `attendance_${currentAttendanceId}`,
            user_id: getCurrentUserId(),
            is_typing: true
        });
    }
    
    clearTimeout(typingTimer);
    typingTimer = setTimeout(() => {
        isTyping = false;
        socket.emit('typing', {
            room: `attendance_${currentAttendanceId}`,
            user_id: getCurrentUserId(),
            is_typing: false
        });
    }, 1000);
}

function showTypingIndicator(userId) {
    if (!$('#typing-indicator').length) {
        $('#chat-messages').append(`
            <div id="typing-indicator" class="typing-indicator">
                <span>Digitando</span>
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `);
        scrollChatToBottom();
    }
}

function hideTypingIndicator() {
    $('#typing-indicator').remove();
}

// Attendance functions
function acceptAttendance(attendanceId) {
    $.ajax({
        url: `/chat/accept/${attendanceId}`,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                showToast('Atendimento aceito!', 'success');
                if (response.redirect) {
                    window.location.href = response.redirect;
                }
            } else {
                showToast(response.message || 'Erro ao aceitar atendimento', 'error');
            }
        },
        error: function() {
            showToast('Erro ao aceitar atendimento', 'error');
        }
    });
}

function closeAttendance(attendanceId, notes) {
    $.ajax({
        url: `/chat/close/${attendanceId}`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ notes: notes }),
        success: function(response) {
            if (response.success) {
                showToast('Atendimento finalizado!', 'success');
                if (response.redirect) {
                    setTimeout(() => {
                        window.location.href = response.redirect;
                    }, 1500);
                }
            } else {
                showToast(response.message || 'Erro ao finalizar atendimento', 'error');
            }
        },
        error: function() {
            showToast('Erro ao finalizar atendimento', 'error');
        }
    });
}

function transferAttendance(attendanceId, operatorId) {
    $.ajax({
        url: `/chat/transfer/${attendanceId}`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ operator_id: operatorId }),
        success: function(response) {
            if (response.success) {
                showToast(response.message, 'success');
                setTimeout(() => {
                    window.location.href = '/chat';
                }, 1500);
            } else {
                showToast(response.message || 'Erro ao transferir atendimento', 'error');
            }
        },
        error: function() {
            showToast('Erro ao transferir atendimento', 'error');
        }
    });
}

// File upload
function handleFileUpload(input) {
    const file = input.files[0];
    if (!file) return;
    
    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
        showToast('Arquivo muito grande. Máximo 10MB.', 'error');
        return;
    }
    
    // Show upload progress
    showLoadingOverlay('Enviando arquivo...');
    
    const formData = new FormData();
    formData.append('file', file);
    
    $.ajax({
        url: '/upload',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                $('#media-url').val(response.url);
                $('#media-type').val(response.type);
                showToast('Arquivo carregado!', 'success');
            } else {
                showToast('Erro ao carregar arquivo', 'error');
            }
        },
        error: function() {
            showToast('Erro ao carregar arquivo', 'error');
        },
        complete: function() {
            hideLoadingOverlay();
        }
    });
}

// Utility functions
function scrollChatToBottom() {
    const chatContainer = $('#chat-messages');
    if (chatContainer.length) {
        chatContainer.scrollTop(chatContainer[0].scrollHeight);
    }
}

function showToast(message, type = 'info') {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    const toastContainer = $('#toast-container');
    if (!toastContainer.length) {
        $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }
    
    const toast = $(toastHtml);
    $('#toast-container').append(toast);
    
    const bsToast = new bootstrap.Toast(toast[0]);
    bsToast.show();
    
    // Remove toast element after it's hidden
    toast.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

function showBrowserNotification(title, body) {
    if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(title, {
            body: body,
            icon: '/static/images/logo.png'
        });
    }
}

function playNotificationSound() {
    if (window.notificationSound) {
        window.notificationSound.play().catch(() => {
            // Ignore errors if sound can't be played
        });
    }
}

function updateNotificationBadge() {
    const badge = $('#notification-count');
    const currentCount = parseInt(badge.text()) || 0;
    badge.text(currentCount + 1).show();
}

function showLoadingOverlay(message = 'Carregando...') {
    const overlay = `
        <div id="loading-overlay" class="spinner-overlay">
            <div class="text-center text-white">
                <div class="spinner-border spinner-border-lg mb-3" role="status"></div>
                <div>${message}</div>
            </div>
        </div>
    `;
    $('body').append(overlay);
}

function hideLoadingOverlay() {
    $('#loading-overlay').remove();
}

function getCurrentUserId() {
    // Get from session or data attribute
    return $('body').data('user-id') || null;
}

function refreshData() {
    // Refresh page data without full reload
    if (window.location.pathname.includes('dashboard')) {
        refreshDashboardStats();
    } else if (window.location.pathname.includes('chat')) {
        refreshAttendancesList();
    }
}

function refreshDashboardStats() {
    $.get('/dashboard/api/stats')
        .done(function(data) {
            updateDashboardCards(data);
        })
        .fail(function() {
            console.error('Failed to refresh dashboard stats');
        });
}

function refreshAttendancesList() {
    $.get('/chat/api/attendances')
        .done(function(data) {
            updateAttendancesList(data.attendances);
        })
        .fail(function() {
            console.error('Failed to refresh attendances list');
        });
}

function updateDashboardCards(stats) {
    // Update dashboard statistics cards
    // Implementation depends on specific dashboard structure
}

function updateAttendancesList(attendances) {
    // Update attendances list
    // Implementation depends on specific list structure
}

// Modal functions
function showCloseAttendanceModal(attendanceId) {
    $('#close-attendance-modal').modal('show');
    $('#close-attendance-form').data('attendance-id', attendanceId);
}

function showTransferAttendanceModal(attendanceId) {
    $('#transfer-attendance-modal').modal('show');
    $('#transfer-attendance-form').data('attendance-id', attendanceId);
}

// Initialize current attendance ID if on chat page
if (window.location.pathname.includes('/chat/attendance/')) {
    currentAttendanceId = window.location.pathname.split('/').pop();
    
    // Join the attendance room
    if (socket) {
        socket.emit('join', {
            room: `attendance_${currentAttendanceId}`,
            user_id: getCurrentUserId()
        });
    }
}
