"""
Formulários para configurações do sistema
"""
from flask_wtf import FlaskForm
from wtforms import StringField, IntegerField, BooleanField, TextAreaField, SelectField, SubmitField
from wtforms.validators import DataRequired, Email, NumberRange, URL, Optional, Length

class SystemSettingsForm(FlaskForm):
    """
    Formulário para configurações gerais do sistema
    """
    system_name = StringField('Nome do Sistema', 
                             validators=[DataRequired(), Length(max=100)],
                             default='Multi Atendimento')
    company_name = StringField('Nome da Empresa', 
                              validators=[Optional(), Length(max=200)])
    support_email = StringField('Email de Suporte', 
                               validators=[Optional(), Email()])
    max_attendances_per_operator = IntegerField('Máximo de atendimentos por operador',
                                               validators=[DataRequired(), NumberRange(min=1, max=20)],
                                               default=5)
    auto_assign_attendances = BooleanField('Atribuição automática de atendimentos')
    enable_file_upload = BooleanField('Permitir upload de arquivos', default=True)
    max_file_size = IntegerField('Tamanho máximo de arquivo (MB)',
                                validators=[DataRequired(), NumberRange(min=1, max=100)],
                                default=10)
    session_timeout = IntegerField('Timeout de sessão (minutos)',
                                  validators=[DataRequired(), NumberRange(min=5, max=480)],
                                  default=30)
    submit = SubmitField('Salvar Configurações')

class WhatsAppSettingsForm(FlaskForm):
    """
    Formulário para configurações do WhatsApp/WAHA
    """
    waha_api_url = StringField('URL da API WAHA',
                              validators=[DataRequired(), URL()],
                              default='http://localhost:3000')
    session_name = StringField('Nome da Sessão',
                              validators=[DataRequired(), Length(max=50)],
                              default='default')
    webhook_url = StringField('URL do Webhook',
                             validators=[DataRequired(), URL()],
                             default='http://localhost:5000/webhook/waha')
    auto_start_session = BooleanField('Iniciar sessão automaticamente')
    enable_typing_indicator = BooleanField('Indicador de digitação', default=True)
    enable_read_receipts = BooleanField('Confirmação de leitura', default=True)
    message_delay = IntegerField('Delay entre mensagens (segundos)',
                                validators=[DataRequired(), NumberRange(min=0, max=10)],
                                default=1)
    submit = SubmitField('Salvar Configurações')

class NotificationSettingsForm(FlaskForm):
    """
    Formulário para configurações de notificações
    """
    enable_email_notifications = BooleanField('Notificações por email')
    enable_browser_notifications = BooleanField('Notificações do navegador', default=True)
    enable_sound_notifications = BooleanField('Notificações sonoras', default=True)
    notify_new_attendance = BooleanField('Notificar novos atendimentos', default=True)
    notify_message_received = BooleanField('Notificar mensagens recebidas', default=True)
    notify_attendance_transferred = BooleanField('Notificar transferências', default=True)
    email_notification_recipients = TextAreaField('Destinatários de email (um por linha)',
                                                 validators=[Optional()],
                                                 description='Digite um email por linha')
    submit = SubmitField('Salvar Configurações')

class BackupSettingsForm(FlaskForm):
    """
    Formulário para configurações de backup
    """
    auto_backup = BooleanField('Backup automático')
    backup_frequency = SelectField('Frequência do backup', choices=[
        ('daily', 'Diário'),
        ('weekly', 'Semanal'),
        ('monthly', 'Mensal')
    ], default='weekly')
    backup_retention_days = IntegerField('Manter backups por (dias)',
                                        validators=[DataRequired(), NumberRange(min=1, max=365)],
                                        default=30)
    backup_location = StringField('Local do backup',
                                 validators=[Optional(), Length(max=500)])
    submit = SubmitField('Salvar Configurações')

class QuickRepliesForm(FlaskForm):
    """
    Formulário para gerenciar respostas rápidas
    """
    title = StringField('Título da resposta',
                       validators=[DataRequired(), Length(max=100)])
    content = TextAreaField('Conteúdo da resposta',
                           validators=[DataRequired(), Length(max=1000)])
    category = SelectField('Categoria', choices=[
        ('greeting', 'Saudação'),
        ('closing', 'Encerramento'),
        ('waiting', 'Aguarde'),
        ('information', 'Informação'),
        ('other', 'Outros')
    ])
    is_active = BooleanField('Ativa', default=True)
    submit = SubmitField('Salvar Resposta')

class IntegrationSettingsForm(FlaskForm):
    """
    Formulário para configurações de integrações
    """
    enable_crm_integration = BooleanField('Integração com CRM')
    crm_api_url = StringField('URL da API do CRM',
                             validators=[Optional(), URL()])
    crm_api_key = StringField('Chave da API do CRM',
                             validators=[Optional(), Length(max=200)])
    
    enable_analytics = BooleanField('Google Analytics')
    analytics_tracking_id = StringField('ID de rastreamento',
                                       validators=[Optional(), Length(max=50)])
    
    enable_chatbot = BooleanField('Chatbot automático')
    chatbot_welcome_message = TextAreaField('Mensagem de boas-vindas do bot',
                                           validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Salvar Configurações')
