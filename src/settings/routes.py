"""
Rotas para configurações do sistema
"""
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash, current_app
from src.extensions.database import db
from src.models.user import User
from src.auth.routes import login_required, admin_required
from src.settings.forms import SystemSettingsForm, WhatsAppSettingsForm, NotificationSettingsForm

# Criação do blueprint
settings_bp = Blueprint('settings', __name__, url_prefix='/settings')

@settings_bp.route('/')
@admin_required
def index():
    """
    Página principal de configurações
    """
    current_user = User.query.get(session['user_id'])
    
    # Configurações atuais do sistema
    system_config = get_system_config()
    whatsapp_config = get_whatsapp_config()
    
    return render_template('settings/index.html',
                         current_user=current_user,
                         system_config=system_config,
                         whatsapp_config=whatsapp_config)

@settings_bp.route('/system', methods=['GET', 'POST'])
@admin_required
def system_settings():
    """
    Configurações gerais do sistema
    """
    form = SystemSettingsForm()
    
    if form.validate_on_submit():
        # Salvar configurações do sistema
        save_system_config({
            'system_name': form.system_name.data,
            'company_name': form.company_name.data,
            'support_email': form.support_email.data,
            'max_attendances_per_operator': form.max_attendances_per_operator.data,
            'auto_assign_attendances': form.auto_assign_attendances.data,
            'enable_file_upload': form.enable_file_upload.data,
            'max_file_size': form.max_file_size.data,
            'session_timeout': form.session_timeout.data
        })
        
        flash('Configurações do sistema salvas com sucesso!', 'success')
        return redirect(url_for('settings.index'))
    
    # Carregar configurações atuais
    config = get_system_config()
    if config:
        form.system_name.data = config.get('system_name', 'Multi Atendimento')
        form.company_name.data = config.get('company_name', '')
        form.support_email.data = config.get('support_email', '')
        form.max_attendances_per_operator.data = config.get('max_attendances_per_operator', 5)
        form.auto_assign_attendances.data = config.get('auto_assign_attendances', False)
        form.enable_file_upload.data = config.get('enable_file_upload', True)
        form.max_file_size.data = config.get('max_file_size', 10)
        form.session_timeout.data = config.get('session_timeout', 30)
    
    return render_template('settings/system.html', form=form)

@settings_bp.route('/whatsapp', methods=['GET', 'POST'])
@admin_required
def whatsapp_settings():
    """
    Configurações do WhatsApp/WAHA
    """
    form = WhatsAppSettingsForm()
    
    if form.validate_on_submit():
        # Salvar configurações do WhatsApp
        save_whatsapp_config({
            'waha_api_url': form.waha_api_url.data,
            'session_name': form.session_name.data,
            'webhook_url': form.webhook_url.data,
            'auto_start_session': form.auto_start_session.data,
            'enable_typing_indicator': form.enable_typing_indicator.data,
            'enable_read_receipts': form.enable_read_receipts.data,
            'message_delay': form.message_delay.data
        })
        
        # Atualizar configurações da aplicação
        current_app.config['WAHA_API_URL'] = form.waha_api_url.data
        current_app.config['WAHA_SESSION_NAME'] = form.session_name.data
        current_app.config['WEBHOOK_URL'] = form.webhook_url.data
        
        flash('Configurações do WhatsApp salvas com sucesso!', 'success')
        return redirect(url_for('settings.index'))
    
    # Carregar configurações atuais
    config = get_whatsapp_config()
    if config:
        form.waha_api_url.data = config.get('waha_api_url', current_app.config.get('WAHA_API_URL'))
        form.session_name.data = config.get('session_name', current_app.config.get('WAHA_SESSION_NAME'))
        form.webhook_url.data = config.get('webhook_url', current_app.config.get('WEBHOOK_URL'))
        form.auto_start_session.data = config.get('auto_start_session', False)
        form.enable_typing_indicator.data = config.get('enable_typing_indicator', True)
        form.enable_read_receipts.data = config.get('enable_read_receipts', True)
        form.message_delay.data = config.get('message_delay', 1)
    
    return render_template('settings/whatsapp.html', form=form)

@settings_bp.route('/notifications', methods=['GET', 'POST'])
@admin_required
def notification_settings():
    """
    Configurações de notificações
    """
    form = NotificationSettingsForm()
    
    if form.validate_on_submit():
        # Salvar configurações de notificações
        save_notification_config({
            'enable_email_notifications': form.enable_email_notifications.data,
            'enable_browser_notifications': form.enable_browser_notifications.data,
            'enable_sound_notifications': form.enable_sound_notifications.data,
            'notify_new_attendance': form.notify_new_attendance.data,
            'notify_message_received': form.notify_message_received.data,
            'notify_attendance_transferred': form.notify_attendance_transferred.data,
            'email_notification_recipients': form.email_notification_recipients.data
        })
        
        flash('Configurações de notificações salvas com sucesso!', 'success')
        return redirect(url_for('settings.index'))
    
    # Carregar configurações atuais
    config = get_notification_config()
    if config:
        form.enable_email_notifications.data = config.get('enable_email_notifications', False)
        form.enable_browser_notifications.data = config.get('enable_browser_notifications', True)
        form.enable_sound_notifications.data = config.get('enable_sound_notifications', True)
        form.notify_new_attendance.data = config.get('notify_new_attendance', True)
        form.notify_message_received.data = config.get('notify_message_received', True)
        form.notify_attendance_transferred.data = config.get('notify_attendance_transferred', True)
        form.email_notification_recipients.data = config.get('email_notification_recipients', '')
    
    return render_template('settings/notifications.html', form=form)

@settings_bp.route('/whatsapp/test-connection', methods=['POST'])
@admin_required
def test_whatsapp_connection():
    """
    Testar conexão com a API WAHA
    """
    try:
        waha_api = current_app.waha_api
        sessions = waha_api.list_sessions()
        
        return jsonify({
            'success': True,
            'message': 'Conexão com WAHA estabelecida com sucesso!',
            'sessions': sessions
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao conectar com WAHA: {str(e)}'
        }), 500

@settings_bp.route('/whatsapp/session-status')
@admin_required
def whatsapp_session_status():
    """
    Verificar status da sessão WhatsApp
    """
    try:
        waha_api = current_app.waha_api
        session_info = waha_api.get_session_info()
        
        return jsonify({
            'success': True,
            'session_info': session_info
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao obter status da sessão: {str(e)}'
        }), 500

@settings_bp.route('/whatsapp/qr-code')
@admin_required
def get_qr_code():
    """
    Obter QR code para autenticação
    """
    try:
        waha_api = current_app.waha_api
        qr_data = waha_api.get_qr_code()
        
        return jsonify({
            'success': True,
            'qr_code': qr_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao obter QR code: {str(e)}'
        }), 500

@settings_bp.route('/whatsapp/start-session', methods=['POST'])
@admin_required
def start_whatsapp_session():
    """
    Iniciar sessão WhatsApp
    """
    try:
        waha_api = current_app.waha_api
        result = waha_api.start_session()
        
        return jsonify({
            'success': True,
            'message': 'Sessão iniciada com sucesso!',
            'result': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao iniciar sessão: {str(e)}'
        }), 500

@settings_bp.route('/whatsapp/stop-session', methods=['POST'])
@admin_required
def stop_whatsapp_session():
    """
    Parar sessão WhatsApp
    """
    try:
        waha_api = current_app.waha_api
        result = waha_api.stop_session()
        
        return jsonify({
            'success': True,
            'message': 'Sessão parada com sucesso!',
            'result': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao parar sessão: {str(e)}'
        }), 500

@settings_bp.route('/backup', methods=['GET', 'POST'])
@admin_required
def backup_settings():
    """
    Backup e restauração do sistema
    """
    if request.method == 'POST':
        action = request.form.get('action')
        
        if action == 'create_backup':
            try:
                backup_file = create_system_backup()
                return jsonify({
                    'success': True,
                    'message': 'Backup criado com sucesso!',
                    'backup_file': backup_file
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'Erro ao criar backup: {str(e)}'
                }), 500
        
        elif action == 'restore_backup':
            # Implementar restauração de backup
            pass
    
    return render_template('settings/backup.html')

# Funções auxiliares para gerenciar configurações

def get_system_config():
    """Obtém configurações do sistema"""
    # Implementar lógica para obter configurações do banco ou arquivo
    return {
        'system_name': 'Multi Atendimento',
        'company_name': '',
        'support_email': '',
        'max_attendances_per_operator': 5,
        'auto_assign_attendances': False,
        'enable_file_upload': True,
        'max_file_size': 10,
        'session_timeout': 30
    }

def save_system_config(config):
    """Salva configurações do sistema"""
    # Implementar lógica para salvar configurações no banco ou arquivo
    pass

def get_whatsapp_config():
    """Obtém configurações do WhatsApp"""
    return {
        'waha_api_url': current_app.config.get('WAHA_API_URL'),
        'session_name': current_app.config.get('WAHA_SESSION_NAME'),
        'webhook_url': current_app.config.get('WEBHOOK_URL'),
        'auto_start_session': False,
        'enable_typing_indicator': True,
        'enable_read_receipts': True,
        'message_delay': 1
    }

def save_whatsapp_config(config):
    """Salva configurações do WhatsApp"""
    # Implementar lógica para salvar configurações
    pass

def get_notification_config():
    """Obtém configurações de notificações"""
    return {
        'enable_email_notifications': False,
        'enable_browser_notifications': True,
        'enable_sound_notifications': True,
        'notify_new_attendance': True,
        'notify_message_received': True,
        'notify_attendance_transferred': True,
        'email_notification_recipients': ''
    }

def save_notification_config(config):
    """Salva configurações de notificações"""
    # Implementar lógica para salvar configurações
    pass

def create_system_backup():
    """Cria backup do sistema"""
    # Implementar lógica de backup
    return 'backup_filename.sql'
