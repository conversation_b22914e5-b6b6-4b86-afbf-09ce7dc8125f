"""
Rotas do dashboard principal
"""
from flask import Blueprint, render_template, session, redirect, url_for, flash, jsonify
from datetime import datetime, timedelta
from sqlalchemy import func, desc
from src.extensions.database import db
from src.models.user import User
from src.models.attendance import Attendance
from src.models.contact import Contact
from src.models.message import Message
from src.auth.routes import login_required

# Criação do blueprint
dashboard_bp = Blueprint('dashboard', __name__, url_prefix='/dashboard')

@dashboard_bp.route('/')
@login_required
def index():
    """
    Dashboard principal do sistema
    """
    current_user = User.query.get(session['user_id'])
    
    # Estatísticas gerais
    stats = get_dashboard_stats(current_user)
    
    # Atendimentos recentes
    recent_attendances = get_recent_attendances(current_user)
    
    # Atendimentos pendentes
    pending_attendances = get_pending_attendances(current_user)
    
    return render_template('dashboard/index.html', 
                         user=current_user,
                         stats=stats,
                         recent_attendances=recent_attendances,
                         pending_attendances=pending_attendances)

@dashboard_bp.route('/stats')
@login_required
def stats():
    """
    Página de estatísticas detalhadas
    """
    current_user = User.query.get(session['user_id'])
    
    # Estatísticas por período
    today_stats = get_period_stats(datetime.now().date(), current_user)
    week_stats = get_period_stats(datetime.now().date() - timedelta(days=7), current_user)
    month_stats = get_period_stats(datetime.now().date() - timedelta(days=30), current_user)
    
    # Gráficos de atendimentos por dia (últimos 30 dias)
    daily_stats = get_daily_stats(current_user)
    
    # Top operadores (se for admin/supervisor)
    top_operators = []
    if current_user.role in ['admin', 'supervisor']:
        top_operators = get_top_operators()
    
    return render_template('dashboard/stats.html',
                         user=current_user,
                         today_stats=today_stats,
                         week_stats=week_stats,
                         month_stats=month_stats,
                         daily_stats=daily_stats,
                         top_operators=top_operators)

@dashboard_bp.route('/api/stats')
@login_required
def api_stats():
    """
    API para obter estatísticas em tempo real
    """
    current_user = User.query.get(session['user_id'])
    stats = get_dashboard_stats(current_user)
    return jsonify(stats)

def get_dashboard_stats(user):
    """
    Obtém estatísticas para o dashboard
    """
    today = datetime.now().date()
    
    # Base query dependendo do papel do usuário
    if user.role == 'attendant':
        # Atendente vê apenas seus próprios atendimentos
        base_query = Attendance.query.filter_by(operator_id=user.id)
    else:
        # Admin e supervisor veem todos os atendimentos
        base_query = Attendance.query
    
    # Estatísticas de hoje
    today_attendances = base_query.filter(
        func.date(Attendance.created_at) == today
    ).count()
    
    today_closed = base_query.filter(
        func.date(Attendance.created_at) == today,
        Attendance.status == 'closed'
    ).count()
    
    # Atendimentos pendentes
    pending = base_query.filter(
        Attendance.status.in_(['open', 'waiting'])
    ).count()
    
    # Atendimentos em andamento
    in_progress = base_query.filter(
        Attendance.status == 'open',
        Attendance.operator_id.isnot(None)
    ).count()
    
    # Tempo médio de atendimento (últimos 30 dias)
    thirty_days_ago = today - timedelta(days=30)
    closed_attendances = base_query.filter(
        Attendance.status == 'closed',
        func.date(Attendance.created_at) >= thirty_days_ago
    ).all()
    
    avg_duration = 0
    if closed_attendances:
        total_duration = sum([att.duration for att in closed_attendances])
        avg_duration = total_duration / len(closed_attendances) / 60  # em minutos
    
    # Total de mensagens hoje
    today_messages = Message.query.join(Attendance).filter(
        func.date(Message.created_at) == today
    )
    
    if user.role == 'attendant':
        today_messages = today_messages.filter(Attendance.operator_id == user.id)
    
    today_messages_count = today_messages.count()
    
    return {
        'today_attendances': today_attendances,
        'today_closed': today_closed,
        'pending': pending,
        'in_progress': in_progress,
        'avg_duration': round(avg_duration, 1),
        'today_messages': today_messages_count,
        'efficiency': round((today_closed / today_attendances * 100) if today_attendances > 0 else 0, 1)
    }

def get_recent_attendances(user, limit=10):
    """
    Obtém atendimentos recentes
    """
    query = Attendance.query
    
    if user.role == 'attendant':
        query = query.filter_by(operator_id=user.id)
    
    return query.order_by(desc(Attendance.updated_at)).limit(limit).all()

def get_pending_attendances(user, limit=10):
    """
    Obtém atendimentos pendentes
    """
    query = Attendance.query.filter(
        Attendance.status.in_(['waiting', 'open'])
    )
    
    if user.role == 'attendant':
        # Atendente vê apenas atendimentos não atribuídos ou seus próprios
        query = query.filter(
            (Attendance.operator_id == None) | (Attendance.operator_id == user.id)
        )
    
    return query.order_by(Attendance.priority.desc(), Attendance.created_at).limit(limit).all()

def get_period_stats(start_date, user):
    """
    Obtém estatísticas para um período específico
    """
    query = Attendance.query.filter(
        func.date(Attendance.created_at) >= start_date
    )
    
    if user.role == 'attendant':
        query = query.filter_by(operator_id=user.id)
    
    total = query.count()
    closed = query.filter(Attendance.status == 'closed').count()
    
    return {
        'total': total,
        'closed': closed,
        'open': total - closed,
        'efficiency': round((closed / total * 100) if total > 0 else 0, 1)
    }

def get_daily_stats(user, days=30):
    """
    Obtém estatísticas diárias para gráficos
    """
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days)
    
    query = db.session.query(
        func.date(Attendance.created_at).label('date'),
        func.count(Attendance.id).label('total'),
        func.sum(func.case([(Attendance.status == 'closed', 1)], else_=0)).label('closed')
    ).filter(
        func.date(Attendance.created_at) >= start_date
    )
    
    if user.role == 'attendant':
        query = query.filter(Attendance.operator_id == user.id)
    
    results = query.group_by(func.date(Attendance.created_at)).all()
    
    # Preencher dias sem dados
    daily_data = []
    current_date = start_date
    
    while current_date <= end_date:
        day_data = next((r for r in results if r.date == current_date), None)
        daily_data.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'total': day_data.total if day_data else 0,
            'closed': day_data.closed if day_data else 0
        })
        current_date += timedelta(days=1)
    
    return daily_data

def get_top_operators(limit=10):
    """
    Obtém ranking dos operadores por performance
    """
    thirty_days_ago = datetime.now().date() - timedelta(days=30)
    
    results = db.session.query(
        User.id,
        User.name,
        User.username,
        func.count(Attendance.id).label('total_attendances'),
        func.sum(func.case([(Attendance.status == 'closed', 1)], else_=0)).label('closed_attendances')
    ).join(
        Attendance, User.id == Attendance.operator_id
    ).filter(
        func.date(Attendance.created_at) >= thirty_days_ago
    ).group_by(
        User.id, User.name, User.username
    ).order_by(
        desc('closed_attendances')
    ).limit(limit).all()
    
    operators = []
    for result in results:
        efficiency = (result.closed_attendances / result.total_attendances * 100) if result.total_attendances > 0 else 0
        operators.append({
            'id': result.id,
            'name': result.name,
            'username': result.username,
            'total_attendances': result.total_attendances,
            'closed_attendances': result.closed_attendances,
            'efficiency': round(efficiency, 1)
        })
    
    return operators
