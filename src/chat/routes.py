"""
<PERSON>otas para chat e atendimento
"""
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash, current_app
from datetime import datetime
from src.extensions.database import db
from src.extensions.websockets import socketio
from src.models.user import User
from src.models.attendance import Attendance
from src.models.contact import Contact
from src.models.message import Message
from src.auth.routes import login_required
from src.chat.forms import MessageForm, TransferAttendanceForm

# Criação do blueprint
chat_bp = Blueprint('chat', __name__, url_prefix='/chat')

@chat_bp.route('/')
@login_required
def index():
    """
    Lista de atendimentos disponíveis
    """
    current_user = User.query.get(session['user_id'])
    
    # Atendimentos do usuário atual
    my_attendances = Attendance.query.filter_by(
        operator_id=current_user.id,
        status='open'
    ).order_by(Attendance.priority.desc(), Attendance.created_at).all()
    
    # Atendimentos pendentes (não atribuídos)
    pending_attendances = Attendance.query.filter_by(
        operator_id=None,
        status='waiting'
    ).order_by(Attendance.priority.desc(), Attendance.created_at).all()
    
    # Se for supervisor ou admin, mostrar todos os atendimentos
    all_attendances = []
    if current_user.role in ['admin', 'supervisor']:
        all_attendances = Attendance.query.filter(
            Attendance.status.in_(['open', 'waiting'])
        ).order_by(Attendance.priority.desc(), Attendance.created_at).all()
    
    return render_template('chat/index.html',
                         user=current_user,
                         my_attendances=my_attendances,
                         pending_attendances=pending_attendances,
                         all_attendances=all_attendances)

@chat_bp.route('/attendance/<int:attendance_id>')
@login_required
def attendance_detail(attendance_id):
    """
    Interface de chat para um atendimento específico
    """
    current_user = User.query.get(session['user_id'])
    attendance = Attendance.query.get_or_404(attendance_id)
    
    # Verificar permissões
    if not can_access_attendance(current_user, attendance):
        flash('Você não tem permissão para acessar este atendimento.', 'error')
        return redirect(url_for('chat.index'))
    
    # Buscar mensagens do atendimento
    messages = Message.query.filter_by(
        attendance_id=attendance_id
    ).order_by(Message.created_at).all()
    
    # Formulário para enviar mensagens
    message_form = MessageForm()
    
    # Formulário para transferir atendimento (se for supervisor/admin)
    transfer_form = None
    if current_user.role in ['admin', 'supervisor']:
        transfer_form = TransferAttendanceForm()
        # Carregar operadores disponíveis
        operators = User.query.filter(
            User.role.in_(['attendant', 'supervisor']),
            User.is_active == True,
            User.id != attendance.operator_id
        ).all()
        transfer_form.operator_id.choices = [(op.id, f"{op.name} ({op.username})") for op in operators]
    
    return render_template('chat/attendance.html',
                         user=current_user,
                         attendance=attendance,
                         messages=messages,
                         message_form=message_form,
                         transfer_form=transfer_form)

@chat_bp.route('/accept/<int:attendance_id>', methods=['POST'])
@login_required
def accept_attendance(attendance_id):
    """
    Aceitar um atendimento pendente
    """
    current_user = User.query.get(session['user_id'])
    attendance = Attendance.query.get_or_404(attendance_id)
    
    if attendance.status != 'waiting' or attendance.operator_id is not None:
        return jsonify({'success': False, 'message': 'Atendimento não está disponível'}), 400
    
    # Atribuir atendimento ao operador
    attendance.operator_id = current_user.id
    attendance.status = 'open'
    attendance.updated_at = datetime.utcnow()
    
    db.session.commit()
    
    # Notificar via websocket
    socketio.emit('attendance_accepted', {
        'attendance_id': attendance_id,
        'operator_id': current_user.id,
        'operator_name': current_user.name
    }, broadcast=True)
    
    return jsonify({'success': True, 'redirect': url_for('chat.attendance_detail', attendance_id=attendance_id)})

@chat_bp.route('/send_message', methods=['POST'])
@login_required
def send_message():
    """
    Enviar mensagem em um atendimento
    """
    current_user = User.query.get(session['user_id'])
    data = request.get_json()
    
    attendance_id = data.get('attendance_id')
    content = data.get('content')
    media_type = data.get('media_type', 'text')
    media_url = data.get('media_url')
    
    if not attendance_id or not content:
        return jsonify({'success': False, 'message': 'Dados incompletos'}), 400
    
    attendance = Attendance.query.get(attendance_id)
    if not attendance or not can_access_attendance(current_user, attendance):
        return jsonify({'success': False, 'message': 'Atendimento não encontrado ou sem permissão'}), 403
    
    # Criar mensagem no banco
    message = Message(
        attendance_id=attendance_id,
        contact_id=attendance.contact_id,
        content=content,
        media_type=media_type,
        media_url=media_url,
        direction='outgoing',
        status='sent'
    )
    
    db.session.add(message)
    
    # Atualizar timestamp do atendimento
    attendance.updated_at = datetime.utcnow()
    db.session.commit()
    
    # Enviar via API WAHA
    try:
        waha_api = current_app.waha_api
        contact = attendance.contact
        
        if media_type == 'text':
            result = waha_api.send_text(contact.whatsapp_id, content)
        elif media_type == 'image':
            result = waha_api.send_image(contact.whatsapp_id, media_url, content)
        elif media_type == 'file':
            result = waha_api.send_file(contact.whatsapp_id, media_url, content)
        else:
            result = waha_api.send_text(contact.whatsapp_id, content)
        
        # Atualizar ID da mensagem no WhatsApp
        if result and 'id' in result:
            message.waha_message_id = result['id']
            db.session.commit()
    
    except Exception as e:
        current_app.logger.error(f"Erro ao enviar mensagem via WAHA: {str(e)}")
        message.status = 'failed'
        db.session.commit()
    
    # Notificar via websocket
    socketio.emit('new_message', {
        'attendance_id': attendance_id,
        'message_id': message.id,
        'content': content,
        'media_type': media_type,
        'media_url': media_url,
        'direction': 'outgoing',
        'timestamp': message.created_at.isoformat(),
        'operator_name': current_user.name
    }, room=f'attendance_{attendance_id}')
    
    return jsonify({
        'success': True,
        'message_id': message.id,
        'timestamp': message.created_at.isoformat()
    })

@chat_bp.route('/close/<int:attendance_id>', methods=['POST'])
@login_required
def close_attendance(attendance_id):
    """
    Finalizar um atendimento
    """
    current_user = User.query.get(session['user_id'])
    attendance = Attendance.query.get_or_404(attendance_id)
    
    if not can_access_attendance(current_user, attendance):
        return jsonify({'success': False, 'message': 'Sem permissão para finalizar este atendimento'}), 403
    
    data = request.get_json()
    notes = data.get('notes', '')
    
    # Finalizar atendimento
    attendance.status = 'closed'
    attendance.closed_at = datetime.utcnow()
    attendance.updated_at = datetime.utcnow()
    if notes:
        attendance.notes = notes
    
    db.session.commit()
    
    # Notificar via websocket
    socketio.emit('attendance_closed', {
        'attendance_id': attendance_id,
        'operator_id': current_user.id,
        'operator_name': current_user.name,
        'closed_at': attendance.closed_at.isoformat()
    }, broadcast=True)
    
    return jsonify({'success': True, 'redirect': url_for('chat.index')})

@chat_bp.route('/transfer/<int:attendance_id>', methods=['POST'])
@login_required
def transfer_attendance(attendance_id):
    """
    Transferir atendimento para outro operador
    """
    current_user = User.query.get(session['user_id'])
    
    # Apenas supervisores e admins podem transferir
    if current_user.role not in ['admin', 'supervisor']:
        return jsonify({'success': False, 'message': 'Sem permissão para transferir atendimentos'}), 403
    
    attendance = Attendance.query.get_or_404(attendance_id)
    data = request.get_json()
    new_operator_id = data.get('operator_id')
    
    if not new_operator_id:
        return jsonify({'success': False, 'message': 'Operador de destino não especificado'}), 400
    
    new_operator = User.query.get(new_operator_id)
    if not new_operator or not new_operator.is_active:
        return jsonify({'success': False, 'message': 'Operador de destino inválido'}), 400
    
    old_operator_id = attendance.operator_id
    
    # Transferir atendimento
    attendance.operator_id = new_operator_id
    attendance.updated_at = datetime.utcnow()
    
    # Adicionar nota sobre a transferência
    transfer_note = f"Atendimento transferido de {current_user.name} para {new_operator.name}"
    if attendance.notes:
        attendance.notes += f"\n{transfer_note}"
    else:
        attendance.notes = transfer_note
    
    db.session.commit()
    
    # Notificar via websocket
    socketio.emit('attendance_transferred', {
        'attendance_id': attendance_id,
        'old_operator_id': old_operator_id,
        'new_operator_id': new_operator_id,
        'new_operator_name': new_operator.name,
        'transferred_by': current_user.name
    }, broadcast=True)
    
    return jsonify({'success': True, 'message': f'Atendimento transferido para {new_operator.name}'})

@chat_bp.route('/api/messages/<int:attendance_id>')
@login_required
def get_messages(attendance_id):
    """
    API para obter mensagens de um atendimento
    """
    current_user = User.query.get(session['user_id'])
    attendance = Attendance.query.get_or_404(attendance_id)
    
    if not can_access_attendance(current_user, attendance):
        return jsonify({'error': 'Sem permissão'}), 403
    
    messages = Message.query.filter_by(
        attendance_id=attendance_id
    ).order_by(Message.created_at).all()
    
    messages_data = []
    for msg in messages:
        messages_data.append({
            'id': msg.id,
            'content': msg.content,
            'media_type': msg.media_type,
            'media_url': msg.media_url,
            'direction': msg.direction,
            'status': msg.status,
            'created_at': msg.created_at.isoformat(),
            'delivered_at': msg.delivered_at.isoformat() if msg.delivered_at else None,
            'read_at': msg.read_at.isoformat() if msg.read_at else None
        })
    
    return jsonify({'messages': messages_data})

@chat_bp.route('/api/attendances')
@login_required
def get_attendances():
    """
    API para obter lista de atendimentos
    """
    current_user = User.query.get(session['user_id'])
    
    # Filtros
    status = request.args.get('status')
    operator_id = request.args.get('operator_id')
    
    query = Attendance.query
    
    # Aplicar filtros baseados no papel do usuário
    if current_user.role == 'attendant':
        # Atendente vê apenas seus atendimentos ou pendentes
        if status == 'pending':
            query = query.filter_by(operator_id=None, status='waiting')
        else:
            query = query.filter_by(operator_id=current_user.id)
    else:
        # Admin/supervisor veem todos
        if operator_id:
            query = query.filter_by(operator_id=operator_id)
    
    if status and status != 'pending':
        query = query.filter_by(status=status)
    
    attendances = query.order_by(
        Attendance.priority.desc(),
        Attendance.created_at
    ).limit(50).all()
    
    attendances_data = []
    for att in attendances:
        attendances_data.append({
            'id': att.id,
            'contact_name': att.contact.name or att.contact.phone,
            'contact_phone': att.contact.phone,
            'operator_name': att.operator.name if att.operator else None,
            'status': att.status,
            'priority': att.priority,
            'subject': att.subject,
            'created_at': att.created_at.isoformat(),
            'updated_at': att.updated_at.isoformat(),
            'duration': att.duration
        })
    
    return jsonify({'attendances': attendances_data})

def can_access_attendance(user, attendance):
    """
    Verifica se o usuário pode acessar um atendimento
    """
    if user.role in ['admin', 'supervisor']:
        return True
    
    if user.role == 'attendant':
        # Atendente pode acessar seus próprios atendimentos ou pendentes
        return attendance.operator_id == user.id or attendance.operator_id is None
    
    return False
