"""
Rotas para autenticação de usuários
"""
from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from werkzeug.security import check_password_hash, generate_password_hash
from datetime import datetime
from src.extensions.database import db
from src.models.user import User
from src.auth.forms import LoginForm, RegistrationForm, ChangePasswordForm

# Criação do blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """
    Página de login
    """
    if 'user_id' in session:
        return redirect(url_for('dashboard.index'))
    
    form = LoginForm()
    
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        
        if user and user.check_password(form.password.data) and user.is_active:
            # Login bem-sucedido
            session['user_id'] = user.id
            session['username'] = user.username
            session['user_role'] = user.role
            
            # Atualizar último login
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            flash(f'Bem-vindo, {user.name}!', 'success')
            
            # Redirecionar para página solicitada ou dashboard
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard.index'))
        else:
            flash('Nome de usuário ou senha inválidos.', 'error')
    
    return render_template('auth/login.html', form=form)

@auth_bp.route('/logout')
def logout():
    """
    Logout do usuário
    """
    session.clear()
    flash('Você foi desconectado com sucesso.', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """
    Registro de novos operadores (apenas para admins)
    """
    # Verificar se usuário está logado e é admin
    # if 'user_id' not in session:
    #     flash('Você precisa estar logado para acessar esta página.', 'error')
    #     return redirect(url_for('auth.login'))
    
    current_user = User.query.get(session['user_id'])
    if not current_user or current_user.role != 'admin':
        flash('Acesso negado. Apenas administradores podem registrar novos usuários.', 'error')
        return redirect(url_for('dashboard.index'))
    
    form = RegistrationForm()
    
    if form.validate_on_submit():
        # Criar novo usuário
        user = User(
            username=form.username.data,
            email=form.email.data,
            name=form.name.data,
            role=form.role.data
        )
        user.set_password(form.password.data)
        
        db.session.add(user)
        db.session.commit()
        
        flash(f'Usuário {user.username} registrado com sucesso!', 'success')
        return redirect(url_for('operators.list_operators'))
    
    return render_template('auth/register.html', form=form)

@auth_bp.route('/change-password', methods=['GET', 'POST'])
def change_password():
    """
    Alteração de senha do usuário logado
    """
    if 'user_id' not in session:
        flash('Você precisa estar logado para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))
    
    current_user = User.query.get(session['user_id'])
    if not current_user:
        flash('Usuário não encontrado.', 'error')
        return redirect(url_for('auth.login'))
    
    form = ChangePasswordForm()
    
    if form.validate_on_submit():
        if current_user.check_password(form.current_password.data):
            current_user.set_password(form.new_password.data)
            db.session.commit()
            flash('Senha alterada com sucesso!', 'success')
            return redirect(url_for('dashboard.index'))
        else:
            flash('Senha atual incorreta.', 'error')
    
    return render_template('auth/change_password.html', form=form)

@auth_bp.route('/profile')
def profile():
    """
    Perfil do usuário logado
    """
    if 'user_id' not in session:
        flash('Você precisa estar logado para acessar esta página.', 'error')
        return redirect(url_for('auth.login'))
    
    current_user = User.query.get(session['user_id'])
    if not current_user:
        flash('Usuário não encontrado.', 'error')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/profile.html', user=current_user)

# Função auxiliar para verificar autenticação
def login_required(f):
    """
    Decorator para rotas que requerem autenticação
    """
    from functools import wraps
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Você precisa estar logado para acessar esta página.', 'error')
            return redirect(url_for('auth.login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """
    Decorator para rotas que requerem privilégios de admin
    """
    from functools import wraps
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Você precisa estar logado para acessar esta página.', 'error')
            return redirect(url_for('auth.login', next=request.url))
        
        current_user = User.query.get(session['user_id'])
        if not current_user or current_user.role != 'admin':
            flash('Acesso negado. Privilégios de administrador necessários.', 'error')
            return redirect(url_for('dashboard.index'))
        
        return f(*args, **kwargs)
    return decorated_function
