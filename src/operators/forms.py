"""
Formulários para gerenciamento de operadores
"""
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, BooleanField, PasswordField, SubmitField
from wtforms.fields import DateField
from wtforms.validators import DataRequired, Email, Length, Optional, EqualTo, ValidationError
from datetime import datetime, timedelta
from src.models.user import User

class EditOperatorForm(FlaskForm):
    """
    Formulário para edição de operadores
    """
    name = StringField('Nome completo', validators=[DataRequired(), Length(min=3, max=100)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    role = SelectField('Função', choices=[
        ('attendant', 'Atendente'),
        ('supervisor', 'Supervisor'),
        ('admin', 'Administrador')
    ], validators=[DataRequired()])
    is_active = BooleanField('Ativo')
    new_password = PasswordField('Nova senha (deixe em branco para manter a atual)', 
                                validators=[Optional(), Length(min=6)])
    confirm_password = PasswordField('Confirmar nova senha', 
                                   validators=[Optional(), EqualTo('new_password')])
    submit = SubmitField('Salvar Alterações')
    
    def __init__(self, operator_id=None, *args, **kwargs):
        super(EditOperatorForm, self).__init__(*args, **kwargs)
        self.operator_id = operator_id
    
    def validate_email(self, email):
        """Valida se o email já existe (exceto para o próprio operador)"""
        user = User.query.filter_by(email=email.data).first()
        if user and (not self.operator_id or user.id != self.operator_id):
            raise ValidationError('Este email já está registrado.')

class OperatorStatsForm(FlaskForm):
    """
    Formulário para filtrar estatísticas de operadores
    """
    start_date = DateField('Data inicial', 
                          validators=[DataRequired()],
                          default=lambda: datetime.now().date() - timedelta(days=30))
    end_date = DateField('Data final', 
                        validators=[DataRequired()],
                        default=lambda: datetime.now().date())
    operator_id = SelectField('Operador específico (opcional)', 
                             choices=[('', 'Todos os operadores')],
                             coerce=lambda x: int(x) if x else None)
    submit = SubmitField('Filtrar')
    
    def validate_end_date(self, end_date):
        """Valida se a data final é posterior à inicial"""
        if self.start_date.data and end_date.data < self.start_date.data:
            raise ValidationError('A data final deve ser posterior à data inicial.')

class OperatorSearchForm(FlaskForm):
    """
    Formulário para buscar operadores
    """
    search = StringField('Buscar por nome, usuário ou email', 
                        validators=[Optional(), Length(max=100)])
    role = SelectField('Função', choices=[
        ('', 'Todas as funções'),
        ('attendant', 'Atendente'),
        ('supervisor', 'Supervisor'),
        ('admin', 'Administrador')
    ])
    status = SelectField('Status', choices=[
        ('', 'Todos'),
        ('active', 'Ativos'),
        ('inactive', 'Inativos')
    ])
    submit = SubmitField('Buscar')

class BulkOperatorActionForm(FlaskForm):
    """
    Formulário para ações em lote nos operadores
    """
    action = SelectField('Ação', choices=[
        ('', 'Selecione uma ação'),
        ('activate', 'Ativar selecionados'),
        ('deactivate', 'Desativar selecionados'),
        ('change_role', 'Alterar função')
    ], validators=[DataRequired()])
    new_role = SelectField('Nova função', choices=[
        ('attendant', 'Atendente'),
        ('supervisor', 'Supervisor'),
        ('admin', 'Administrador')
    ])
    submit = SubmitField('Executar Ação')
