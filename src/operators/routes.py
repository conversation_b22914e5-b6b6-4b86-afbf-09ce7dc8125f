"""
Rotas para gerenciamento de operadores
"""
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash
from datetime import datetime, timedelta
from sqlalchemy import func, desc
from src.extensions.database import db
from src.models.user import User
from src.models.attendance import Attendance
from src.auth.routes import login_required, admin_required
from src.operators.forms import EditOperatorForm, OperatorStatsForm

# Criação do blueprint
operators_bp = Blueprint('operators', __name__, url_prefix='/operators')

@operators_bp.route('/')
@login_required
def list_operators():
    """
    Lista todos os operadores do sistema
    """
    current_user = User.query.get(session['user_id'])
    
    # Apenas admins e supervisores podem ver lista completa
    if current_user.role not in ['admin', 'supervisor']:
        flash('Acesso negado. Privilégios insuficientes.', 'error')
        return redirect(url_for('dashboard.index'))
    
    # Filtros
    role_filter = request.args.get('role')
    status_filter = request.args.get('status')
    search = request.args.get('search')
    
    query = User.query
    
    # Aplicar filtros
    if role_filter:
        query = query.filter_by(role=role_filter)
    
    if status_filter == 'active':
        query = query.filter_by(is_active=True)
    elif status_filter == 'inactive':
        query = query.filter_by(is_active=False)
    
    if search:
        query = query.filter(
            (User.name.contains(search)) |
            (User.username.contains(search)) |
            (User.email.contains(search))
        )
    
    operators = query.order_by(User.name).all()
    
    # Estatísticas dos operadores (últimos 30 dias)
    operator_stats = get_operators_stats(operators)
    
    return render_template('operators/list.html',
                         current_user=current_user,
                         operators=operators,
                         operator_stats=operator_stats,
                         role_filter=role_filter,
                         status_filter=status_filter,
                         search=search)

@operators_bp.route('/<int:operator_id>')
@login_required
def operator_detail(operator_id):
    """
    Detalhes de um operador específico
    """
    current_user = User.query.get(session['user_id'])
    operator = User.query.get_or_404(operator_id)
    
    # Verificar permissões
    if current_user.role not in ['admin', 'supervisor'] and current_user.id != operator_id:
        flash('Acesso negado.', 'error')
        return redirect(url_for('dashboard.index'))
    
    # Estatísticas do operador
    stats = get_operator_detailed_stats(operator)
    
    # Atendimentos recentes
    recent_attendances = Attendance.query.filter_by(
        operator_id=operator_id
    ).order_by(desc(Attendance.updated_at)).limit(20).all()
    
    return render_template('operators/detail.html',
                         current_user=current_user,
                         operator=operator,
                         stats=stats,
                         recent_attendances=recent_attendances)

@operators_bp.route('/<int:operator_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_operator(operator_id):
    """
    Editar informações de um operador
    """
    operator = User.query.get_or_404(operator_id)
    form = EditOperatorForm(obj=operator)
    
    if form.validate_on_submit():
        # Atualizar dados do operador
        operator.name = form.name.data
        operator.email = form.email.data
        operator.role = form.role.data
        operator.is_active = form.is_active.data
        
        # Alterar senha se fornecida
        if form.new_password.data:
            operator.set_password(form.new_password.data)
        
        db.session.commit()
        flash(f'Operador {operator.name} atualizado com sucesso!', 'success')
        return redirect(url_for('operators.operator_detail', operator_id=operator_id))
    
    return render_template('operators/edit.html', form=form, operator=operator)

@operators_bp.route('/<int:operator_id>/toggle-status', methods=['POST'])
@admin_required
def toggle_operator_status(operator_id):
    """
    Ativar/desativar um operador
    """
    operator = User.query.get_or_404(operator_id)
    
    # Não permitir desativar o próprio usuário
    current_user = User.query.get(session['user_id'])
    if operator_id == current_user.id:
        return jsonify({'success': False, 'message': 'Você não pode desativar sua própria conta'}), 400
    
    operator.is_active = not operator.is_active
    db.session.commit()
    
    status_text = 'ativado' if operator.is_active else 'desativado'
    
    return jsonify({
        'success': True,
        'message': f'Operador {operator.name} {status_text} com sucesso',
        'is_active': operator.is_active
    })

@operators_bp.route('/stats')
@login_required
def operators_stats():
    """
    Estatísticas gerais dos operadores
    """
    current_user = User.query.get(session['user_id'])
    
    if current_user.role not in ['admin', 'supervisor']:
        flash('Acesso negado.', 'error')
        return redirect(url_for('dashboard.index'))
    
    form = OperatorStatsForm()
    
    # Período padrão: últimos 30 dias
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    
    if form.validate_on_submit():
        start_date = form.start_date.data
        end_date = form.end_date.data
    
    # Estatísticas por operador no período
    stats_data = get_period_operators_stats(start_date, end_date)
    
    # Estatísticas gerais
    general_stats = get_general_operators_stats(start_date, end_date)
    
    return render_template('operators/stats.html',
                         current_user=current_user,
                         form=form,
                         stats_data=stats_data,
                         general_stats=general_stats,
                         start_date=start_date,
                         end_date=end_date)

@operators_bp.route('/api/performance/<int:operator_id>')
@login_required
def operator_performance_api(operator_id):
    """
    API para obter dados de performance de um operador
    """
    current_user = User.query.get(session['user_id'])
    
    # Verificar permissões
    if current_user.role not in ['admin', 'supervisor'] and current_user.id != operator_id:
        return jsonify({'error': 'Acesso negado'}), 403
    
    days = request.args.get('days', 30, type=int)
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days)
    
    # Dados diários de performance
    daily_data = get_operator_daily_performance(operator_id, start_date, end_date)
    
    return jsonify({'daily_performance': daily_data})

def get_operators_stats(operators):
    """
    Obtém estatísticas básicas dos operadores
    """
    thirty_days_ago = datetime.now().date() - timedelta(days=30)
    
    stats = {}
    for operator in operators:
        attendances = Attendance.query.filter(
            Attendance.operator_id == operator.id,
            func.date(Attendance.created_at) >= thirty_days_ago
        ).all()
        
        total = len(attendances)
        closed = len([a for a in attendances if a.status == 'closed'])
        avg_duration = 0
        
        if closed > 0:
            total_duration = sum([a.duration for a in attendances if a.status == 'closed'])
            avg_duration = total_duration / closed / 60  # em minutos
        
        stats[operator.id] = {
            'total_attendances': total,
            'closed_attendances': closed,
            'efficiency': round((closed / total * 100) if total > 0 else 0, 1),
            'avg_duration': round(avg_duration, 1)
        }
    
    return stats

def get_operator_detailed_stats(operator):
    """
    Obtém estatísticas detalhadas de um operador
    """
    today = datetime.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)
    
    # Estatísticas de hoje
    today_attendances = Attendance.query.filter(
        Attendance.operator_id == operator.id,
        func.date(Attendance.created_at) == today
    ).count()
    
    today_closed = Attendance.query.filter(
        Attendance.operator_id == operator.id,
        func.date(Attendance.created_at) == today,
        Attendance.status == 'closed'
    ).count()
    
    # Estatísticas da semana
    week_attendances = Attendance.query.filter(
        Attendance.operator_id == operator.id,
        func.date(Attendance.created_at) >= week_ago
    ).count()
    
    week_closed = Attendance.query.filter(
        Attendance.operator_id == operator.id,
        func.date(Attendance.created_at) >= week_ago,
        Attendance.status == 'closed'
    ).count()
    
    # Estatísticas do mês
    month_attendances = Attendance.query.filter(
        Attendance.operator_id == operator.id,
        func.date(Attendance.created_at) >= month_ago
    ).count()
    
    month_closed = Attendance.query.filter(
        Attendance.operator_id == operator.id,
        func.date(Attendance.created_at) >= month_ago,
        Attendance.status == 'closed'
    ).count()
    
    # Tempo médio de atendimento
    closed_attendances = Attendance.query.filter(
        Attendance.operator_id == operator.id,
        Attendance.status == 'closed',
        func.date(Attendance.created_at) >= month_ago
    ).all()
    
    avg_duration = 0
    if closed_attendances:
        total_duration = sum([a.duration for a in closed_attendances])
        avg_duration = total_duration / len(closed_attendances) / 60  # em minutos
    
    return {
        'today': {
            'total': today_attendances,
            'closed': today_closed,
            'efficiency': round((today_closed / today_attendances * 100) if today_attendances > 0 else 0, 1)
        },
        'week': {
            'total': week_attendances,
            'closed': week_closed,
            'efficiency': round((week_closed / week_attendances * 100) if week_attendances > 0 else 0, 1)
        },
        'month': {
            'total': month_attendances,
            'closed': month_closed,
            'efficiency': round((month_closed / month_attendances * 100) if month_attendances > 0 else 0, 1)
        },
        'avg_duration': round(avg_duration, 1)
    }

def get_period_operators_stats(start_date, end_date):
    """
    Obtém estatísticas dos operadores para um período específico
    """
    results = db.session.query(
        User.id,
        User.name,
        User.username,
        func.count(Attendance.id).label('total_attendances'),
        func.sum(func.case([(Attendance.status == 'closed', 1)], else_=0)).label('closed_attendances')
    ).join(
        Attendance, User.id == Attendance.operator_id
    ).filter(
        func.date(Attendance.created_at) >= start_date,
        func.date(Attendance.created_at) <= end_date
    ).group_by(
        User.id, User.name, User.username
    ).order_by(
        desc('closed_attendances')
    ).all()
    
    stats_data = []
    for result in results:
        efficiency = (result.closed_attendances / result.total_attendances * 100) if result.total_attendances > 0 else 0
        stats_data.append({
            'id': result.id,
            'name': result.name,
            'username': result.username,
            'total_attendances': result.total_attendances,
            'closed_attendances': result.closed_attendances,
            'efficiency': round(efficiency, 1)
        })
    
    return stats_data

def get_general_operators_stats(start_date, end_date):
    """
    Obtém estatísticas gerais dos operadores
    """
    total_operators = User.query.filter_by(is_active=True).count()
    
    total_attendances = Attendance.query.filter(
        func.date(Attendance.created_at) >= start_date,
        func.date(Attendance.created_at) <= end_date
    ).count()
    
    total_closed = Attendance.query.filter(
        func.date(Attendance.created_at) >= start_date,
        func.date(Attendance.created_at) <= end_date,
        Attendance.status == 'closed'
    ).count()
    
    operators_with_attendances = db.session.query(
        func.count(func.distinct(Attendance.operator_id))
    ).filter(
        func.date(Attendance.created_at) >= start_date,
        func.date(Attendance.created_at) <= end_date,
        Attendance.operator_id.isnot(None)
    ).scalar()
    
    return {
        'total_operators': total_operators,
        'active_operators': operators_with_attendances,
        'total_attendances': total_attendances,
        'total_closed': total_closed,
        'general_efficiency': round((total_closed / total_attendances * 100) if total_attendances > 0 else 0, 1),
        'avg_attendances_per_operator': round(total_attendances / operators_with_attendances if operators_with_attendances > 0 else 0, 1)
    }

def get_operator_daily_performance(operator_id, start_date, end_date):
    """
    Obtém performance diária de um operador
    """
    results = db.session.query(
        func.date(Attendance.created_at).label('date'),
        func.count(Attendance.id).label('total'),
        func.sum(func.case([(Attendance.status == 'closed', 1)], else_=0)).label('closed')
    ).filter(
        Attendance.operator_id == operator_id,
        func.date(Attendance.created_at) >= start_date,
        func.date(Attendance.created_at) <= end_date
    ).group_by(
        func.date(Attendance.created_at)
    ).all()
    
    # Preencher dias sem dados
    daily_data = []
    current_date = start_date
    
    while current_date <= end_date:
        day_data = next((r for r in results if r.date == current_date), None)
        daily_data.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'total': day_data.total if day_data else 0,
            'closed': day_data.closed if day_data else 0,
            'efficiency': round((day_data.closed / day_data.total * 100) if day_data and day_data.total > 0 else 0, 1)
        })
        current_date += timedelta(days=1)
    
    return daily_data
