"""
Arquivo principal da aplicação Flask usando factory pattern
"""
import os
from flask import Flask
from src.extensions import database, forms, websockets
from src.waha_api import client
from src.waha_api.webhook import webhook_bp
from src.auth.routes import auth_bp
from src.dashboard.routes import dashboard_bp
from src.chat.routes import chat_bp
from src.operators.routes import operators_bp
from src.settings.routes import settings_bp

def create_app(config=None):
    """
    Factory function para criar a aplicação Flask

    Parâmetros:
    - config: Configurações adicionais (opcional)

    Retorna:
    - Aplicação Flask configurada
    """
    app = Flask(__name__)

    # Configurações padrão
    app.config.from_mapping(
        SECRET_KEY=os.environ.get('SECRET_KEY', 'dev_key_insecure'),
        SQLALCHEMY_DATABASE_URI=os.environ.get('DATABASE_URL', 'sqlite:///multi_atendimento.db'),
        SQLALCHEMY_TRACK_MODIFICATIONS=False,
        WAHA_API_URL=os.environ.get('WAHA_API_URL', 'http://localhost:3000'),
        WAHA_SESSION_NAME=os.environ.get('WAHA_SESSION_NAME', 'default'),
        WEBHOOK_URL=os.environ.get('WEBHOOK_URL', 'http://localhost:5000/webhook/waha')
    )

    # Sobrescrever com configurações personalizadas
    if config:
        app.config.update(config)

    # Inicializar extensões
    database.init_app(app)
    forms.init_app(app)
    websockets.init_app(app)
    client.init_app(app)

    # Registrar blueprints
    app.register_blueprint(webhook_bp)

    # Importar modelos para que sejam registrados com SQLAlchemy
    from src import models

    # Criar tabelas do banco de dados
    with app.app_context():
        database.db.create_all()

    return app
