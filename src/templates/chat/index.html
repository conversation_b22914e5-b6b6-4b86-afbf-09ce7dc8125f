{% extends "base.html" %}

{% block title %}Atendimentos - Multi Atendimento{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-comments"></i> Atendimentos</h2>
    <div>
        <button class="btn btn-outline-primary" onclick="refreshAttendances()">
            <i class="fas fa-sync-alt"></i> Atualizar
        </button>
    </div>
</div>

<div class="row">
    <!-- Meus Atendimentos -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-user text-primary"></i> Meus Atendimentos</h5>
                <span class="badge bg-primary">{{ my_attendances|length }}</span>
            </div>
            <div class="card-body">
                {% if my_attendances %}
                    <div class="list-group list-group-flush">
                        {% for attendance in my_attendances %}
                        <div class="list-group-item attendance-item {% if attendance.priority == 2 %}priority-urgent{% elif attendance.priority == 1 %}priority-high{% endif %}" 
                             data-id="{{ attendance.id }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        {{ attendance.contact.name or attendance.contact.phone }}
                                        {% if attendance.priority == 2 %}
                                            <span class="badge bg-danger ms-2">Urgente</span>
                                        {% elif attendance.priority == 1 %}
                                            <span class="badge bg-warning ms-2">Alta</span>
                                        {% endif %}
                                    </h6>
                                    <p class="mb-1 text-muted text-truncate">{{ attendance.subject }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i> 
                                        {{ attendance.created_at.strftime('%d/%m/%Y %H:%M') }}
                                        {% if attendance.status == 'open' %}
                                            - Duração: {{ "%.0f"|format(attendance.duration / 60) }} min
                                        {% endif %}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success mb-2">Ativo</span><br>
                                    <a href="{{ url_for('chat.attendance_detail', attendance_id=attendance.id) }}" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-comments"></i> Abrir
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>Você não tem atendimentos ativos</p>
                        <small>Aceite um atendimento pendente para começar</small>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Atendimentos Pendentes -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-clock text-warning"></i> Pendentes</h5>
                <span class="badge bg-warning">{{ pending_attendances|length }}</span>
            </div>
            <div class="card-body">
                {% if pending_attendances %}
                    <div class="list-group list-group-flush">
                        {% for attendance in pending_attendances %}
                        <div class="list-group-item attendance-item {% if attendance.priority == 2 %}priority-urgent{% elif attendance.priority == 1 %}priority-high{% endif %}" 
                             data-id="{{ attendance.id }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        {{ attendance.contact.name or attendance.contact.phone }}
                                        {% if attendance.priority == 2 %}
                                            <span class="badge bg-danger ms-2">Urgente</span>
                                        {% elif attendance.priority == 1 %}
                                            <span class="badge bg-warning ms-2">Alta</span>
                                        {% endif %}
                                    </h6>
                                    <p class="mb-1 text-muted text-truncate">{{ attendance.subject }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i> 
                                        {{ attendance.created_at.strftime('%d/%m/%Y %H:%M') }}
                                        - Aguardando há {{ "%.0f"|format(attendance.duration / 60) }} min
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-warning mb-2">Pendente</span><br>
                                    <button class="btn btn-sm btn-success accept-attendance-btn" 
                                            data-attendance-id="{{ attendance.id }}">
                                        <i class="fas fa-check"></i> Aceitar
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <p>Nenhum atendimento pendente!</p>
                        <small>Todos os atendimentos estão sendo atendidos</small>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Todos os Atendimentos (Admin/Supervisor) -->
{% if user.role in ['admin', 'supervisor'] and all_attendances %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-list text-info"></i> Todos os Atendimentos</h5>
                <div>
                    <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#all-attendances">
                        <i class="fas fa-eye"></i> Mostrar/Ocultar
                    </button>
                </div>
            </div>
            <div class="collapse" id="all-attendances">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Contato</th>
                                    <th>Operador</th>
                                    <th>Status</th>
                                    <th>Prioridade</th>
                                    <th>Criado em</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attendance in all_attendances %}
                                <tr>
                                    <td>#{{ attendance.id }}</td>
                                    <td>
                                        <strong>{{ attendance.contact.name or attendance.contact.phone }}</strong><br>
                                        <small class="text-muted">{{ attendance.subject }}</small>
                                    </td>
                                    <td>
                                        {% if attendance.operator %}
                                            {{ attendance.operator.name }}
                                        {% else %}
                                            <span class="text-muted">Não atribuído</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.status == 'open' %}
                                            <span class="badge bg-success">Ativo</span>
                                        {% elif attendance.status == 'waiting' %}
                                            <span class="badge bg-warning">Pendente</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ attendance.status|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.priority == 2 %}
                                            <span class="badge bg-danger">Urgente</span>
                                        {% elif attendance.priority == 1 %}
                                            <span class="badge bg-warning">Alta</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Normal</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ attendance.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('chat.attendance_detail', attendance_id=attendance.id) }}" 
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if attendance.status in ['open', 'waiting'] %}
                                            <button class="btn btn-outline-warning transfer-attendance-btn" 
                                                    data-attendance-id="{{ attendance.id }}">
                                                <i class="fas fa-exchange-alt"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Estatísticas Rápidas -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary">{{ my_attendances|length }}</h4>
                <small class="text-muted">Meus Atendimentos</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning">{{ pending_attendances|length }}</h4>
                <small class="text-muted">Pendentes</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success">
                    {{ my_attendances|selectattr('status', 'equalto', 'open')|list|length }}
                </h4>
                <small class="text-muted">Ativos</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info">
                    {% if user.role in ['admin', 'supervisor'] %}
                        {{ all_attendances|length }}
                    {% else %}
                        {{ my_attendances|length + pending_attendances|length }}
                    {% endif %}
                </h4>
                <small class="text-muted">Total Visível</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Auto-refresh attendances every 30 seconds
setInterval(function() {
    refreshAttendances();
}, 30000);

function refreshAttendances() {
    location.reload();
}

// Handle accept attendance
$(document).on('click', '.accept-attendance-btn', function() {
    const attendanceId = $(this).data('attendance-id');
    const btn = $(this);
    
    btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');
    
    acceptAttendance(attendanceId);
});

// Handle transfer attendance
$(document).on('click', '.transfer-attendance-btn', function() {
    const attendanceId = $(this).data('attendance-id');
    showTransferAttendanceModal(attendanceId);
});

// Highlight urgent attendances
$('.priority-urgent').addClass('border-start-danger');
$('.priority-high').addClass('border-start-warning');

// Add click handler for attendance items
$('.attendance-item').on('click', function(e) {
    if (!$(e.target).closest('button, a').length) {
        const attendanceId = $(this).data('id');
        window.location.href = `/chat/attendance/${attendanceId}`;
    }
});
</script>
{% endblock %}
