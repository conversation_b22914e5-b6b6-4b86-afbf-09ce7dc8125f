{% extends "base.html" %}

{% block title %}Atendimento #{{ attendance.id }} - Multi Atendimento{% endblock %}

{% block content %}
<div class="row">
    <!-- Chat Area -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <i class="fas fa-user"></i> 
                        {{ attendance.contact.name or attendance.contact.phone }}
                        {% if attendance.priority == 2 %}
                            <span class="badge bg-danger ms-2">Urgente</span>
                        {% elif attendance.priority == 1 %}
                            <span class="badge bg-warning ms-2">Alta</span>
                        {% endif %}
                    </h5>
                    <small class="text-muted">{{ attendance.subject }}</small>
                </div>
                <div>
                    {% if attendance.status == 'open' %}
                        <span class="badge bg-success">Ativo</span>
                    {% elif attendance.status == 'waiting' %}
                        <span class="badge bg-warning">Pendente</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ attendance.status|title }}</span>
                    {% endif %}
                </div>
            </div>
            
            <!-- Messages Area -->
            <div class="card-body p-0">
                <div id="chat-messages" class="chat-container p-3">
                    {% for message in messages %}
                    <div class="message {{ message.direction }}" data-id="{{ message.id }}">
                        {% if message.media_type == 'image' and message.media_url %}
                            <img src="{{ message.media_url }}" class="img-fluid rounded mb-2" style="max-width: 200px;">
                        {% elif message.media_type == 'file' and message.media_url %}
                            <a href="{{ message.media_url }}" target="_blank" class="btn btn-sm btn-outline-primary mb-2">
                                <i class="fas fa-download"></i> Download
                            </a>
                        {% endif %}
                        
                        <div class="message-content">{{ message.content }}</div>
                        <div class="message-time">
                            {{ message.created_at.strftime('%H:%M') }}
                        </div>
                        
                        {% if message.direction == 'outgoing' %}
                        <div class="message-status">
                            {% if message.status == 'sent' %}
                                <i class="fas fa-check text-muted"></i>
                            {% elif message.status == 'delivered' %}
                                <i class="fas fa-check-double text-info"></i>
                            {% elif message.status == 'read' %}
                                <i class="fas fa-check-double text-primary"></i>
                            {% elif message.status == 'failed' %}
                                <i class="fas fa-exclamation-triangle text-danger"></i>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Message Input -->
            <div class="card-footer">
                <form id="message-form" class="d-flex gap-2">
                    <input type="hidden" id="attendance-id" value="{{ attendance.id }}">
                    <input type="hidden" id="media-url" value="">
                    
                    <div class="flex-grow-1">
                        <div class="input-group">
                            <textarea id="message-content" class="form-control" 
                                     placeholder="Digite sua mensagem..." rows="1" 
                                     style="resize: none;"></textarea>
                            <button type="button" class="btn btn-outline-secondary" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <label class="dropdown-item" for="file-input">
                                        <i class="fas fa-image"></i> Imagem
                                        <input type="file" id="file-input" class="d-none" 
                                               accept="image/*" onchange="handleFileUpload(this)">
                                    </label>
                                </li>
                                <li>
                                    <label class="dropdown-item" for="file-input-doc">
                                        <i class="fas fa-file"></i> Documento
                                        <input type="file" id="file-input-doc" class="d-none" 
                                               accept=".pdf,.doc,.docx" onchange="handleFileUpload(this)">
                                    </label>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <button type="submit" id="send-btn" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
                
                <!-- Quick Replies -->
                <div class="mt-2">
                    <small class="text-muted">Respostas rápidas:</small>
                    <div class="btn-group-sm mt-1">
                        <button type="button" class="btn btn-outline-secondary btn-sm quick-reply-btn" 
                                data-message="Olá! Como posso ajudá-lo hoje?">
                            Saudação
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm quick-reply-btn" 
                                data-message="Por favor, aguarde um momento...">
                            Aguarde
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm quick-reply-btn" 
                                data-message="Obrigado pelo contato!">
                            Agradecimento
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Contact Info -->
        <div class="card mb-3">
            <div class="card-header">
                <h6><i class="fas fa-user-circle"></i> Informações do Contato</h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>Nome:</strong> {{ attendance.contact.name or 'Não informado' }}
                </div>
                <div class="mb-2">
                    <strong>Telefone:</strong> {{ attendance.contact.phone }}
                </div>
                <div class="mb-2">
                    <strong>WhatsApp ID:</strong> 
                    <small class="text-muted">{{ attendance.contact.whatsapp_id }}</small>
                </div>
                {% if attendance.contact.is_business %}
                <div class="mb-2">
                    <span class="badge bg-info">Conta Business</span>
                </div>
                {% endif %}
                <div class="mb-2">
                    <strong>Primeiro contato:</strong><br>
                    <small class="text-muted">{{ attendance.contact.created_at.strftime('%d/%m/%Y %H:%M') }}</small>
                </div>
            </div>
        </div>
        
        <!-- Attendance Info -->
        <div class="card mb-3">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> Detalhes do Atendimento</h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>ID:</strong> #{{ attendance.id }}
                </div>
                <div class="mb-2">
                    <strong>Status:</strong> 
                    {% if attendance.status == 'open' %}
                        <span class="badge bg-success">Ativo</span>
                    {% elif attendance.status == 'waiting' %}
                        <span class="badge bg-warning">Pendente</span>
                    {% elif attendance.status == 'closed' %}
                        <span class="badge bg-secondary">Finalizado</span>
                    {% endif %}
                </div>
                <div class="mb-2">
                    <strong>Prioridade:</strong>
                    {% if attendance.priority == 2 %}
                        <span class="badge bg-danger">Urgente</span>
                    {% elif attendance.priority == 1 %}
                        <span class="badge bg-warning">Alta</span>
                    {% else %}
                        <span class="badge bg-secondary">Normal</span>
                    {% endif %}
                </div>
                <div class="mb-2">
                    <strong>Operador:</strong> 
                    {% if attendance.operator %}
                        {{ attendance.operator.name }}
                    {% else %}
                        <span class="text-muted">Não atribuído</span>
                    {% endif %}
                </div>
                <div class="mb-2">
                    <strong>Criado em:</strong><br>
                    <small>{{ attendance.created_at.strftime('%d/%m/%Y %H:%M') }}</small>
                </div>
                <div class="mb-2">
                    <strong>Duração:</strong> {{ "%.0f"|format(attendance.duration / 60) }} minutos
                </div>
                {% if attendance.notes %}
                <div class="mb-2">
                    <strong>Observações:</strong><br>
                    <small class="text-muted">{{ attendance.notes }}</small>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Actions -->
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-cogs"></i> Ações</h6>
            </div>
            <div class="card-body">
                {% if attendance.status == 'waiting' and not attendance.operator_id %}
                <button class="btn btn-success w-100 mb-2 accept-attendance-btn" 
                        data-attendance-id="{{ attendance.id }}">
                    <i class="fas fa-check"></i> Aceitar Atendimento
                </button>
                {% endif %}
                
                {% if attendance.status == 'open' and (attendance.operator_id == user.id or user.role in ['admin', 'supervisor']) %}
                <button class="btn btn-danger w-100 mb-2" 
                        onclick="showCloseAttendanceModal({{ attendance.id }})">
                    <i class="fas fa-times"></i> Finalizar Atendimento
                </button>
                {% endif %}
                
                {% if user.role in ['admin', 'supervisor'] and attendance.status in ['open', 'waiting'] %}
                <button class="btn btn-warning w-100 mb-2" 
                        onclick="showTransferAttendanceModal({{ attendance.id }})">
                    <i class="fas fa-exchange-alt"></i> Transferir
                </button>
                {% endif %}
                
                <a href="{{ url_for('chat.index') }}" class="btn btn-secondary w-100">
                    <i class="fas fa-arrow-left"></i> Voltar à Lista
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Close Attendance Modal -->
<div class="modal fade" id="close-attendance-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Finalizar Atendimento</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="close-attendance-form">
                    <div class="mb-3">
                        <label for="close-notes" class="form-label">Observações finais:</label>
                        <textarea id="close-notes" class="form-control" rows="3" 
                                 placeholder="Descreva a resolução do atendimento..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-danger" onclick="confirmCloseAttendance()">
                    Finalizar Atendimento
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Attendance Modal -->
{% if transfer_form %}
<div class="modal fade" id="transfer-attendance-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Transferir Atendimento</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="transfer-attendance-form">
                    <div class="mb-3">
                        <label for="transfer-operator" class="form-label">Transferir para:</label>
                        <select id="transfer-operator" class="form-select">
                            <option value="">Selecione um operador...</option>
                            {% for value, label in transfer_form.operator_id.choices %}
                            <option value="{{ value }}">{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-warning" onclick="confirmTransferAttendance()">
                    Transferir
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
// Set current attendance ID for WebSocket
currentAttendanceId = {{ attendance.id }};

// Join attendance room
if (socket) {
    socket.emit('join', {
        room: `attendance_${currentAttendanceId}`,
        user_id: {{ user.id }}
    });
}

// Scroll to bottom on load
$(document).ready(function() {
    scrollChatToBottom();
    $('#message-content').focus();
});

// Handle message form submission
$('#message-form').on('submit', function(e) {
    e.preventDefault();
    sendMessage();
});

// Handle quick reply buttons
$('.quick-reply-btn').on('click', function() {
    const message = $(this).data('message');
    $('#message-content').val(message);
    $('#message-content').focus();
});

// Auto-resize textarea
$('#message-content').on('input', function() {
    this.style.height = 'auto';
    this.style.height = (this.scrollHeight) + 'px';
});

// Handle Enter key (send message)
$('#message-content').on('keydown', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        $('#message-form').submit();
    }
});

// File upload handler
function handleFileUpload(input) {
    const file = input.files[0];
    if (!file) return;
    
    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
        showToast('Arquivo muito grande. Máximo 10MB.', 'error');
        return;
    }
    
    // Show upload progress
    showLoadingOverlay('Enviando arquivo...');
    
    const formData = new FormData();
    formData.append('file', file);
    
    $.ajax({
        url: '/upload',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                $('#media-url').val(response.url);
                showToast('Arquivo carregado!', 'success');
                
                // Add file info to message
                const fileName = file.name;
                const currentMessage = $('#message-content').val();
                $('#message-content').val(currentMessage + (currentMessage ? '\n' : '') + `📎 ${fileName}`);
            } else {
                showToast('Erro ao carregar arquivo', 'error');
            }
        },
        error: function() {
            showToast('Erro ao carregar arquivo', 'error');
        },
        complete: function() {
            hideLoadingOverlay();
            input.value = ''; // Reset file input
        }
    });
}

// Close attendance functions
function showCloseAttendanceModal(attendanceId) {
    $('#close-attendance-modal').modal('show');
    $('#close-attendance-form').data('attendance-id', attendanceId);
}

function confirmCloseAttendance() {
    const attendanceId = $('#close-attendance-form').data('attendance-id');
    const notes = $('#close-notes').val();
    
    closeAttendance(attendanceId, notes);
    $('#close-attendance-modal').modal('hide');
}

// Transfer attendance functions
function showTransferAttendanceModal(attendanceId) {
    $('#transfer-attendance-modal').modal('show');
    $('#transfer-attendance-form').data('attendance-id', attendanceId);
}

function confirmTransferAttendance() {
    const attendanceId = $('#transfer-attendance-form').data('attendance-id');
    const operatorId = $('#transfer-operator').val();
    
    if (!operatorId) {
        showToast('Selecione um operador', 'warning');
        return;
    }
    
    transferAttendance(attendanceId, operatorId);
    $('#transfer-attendance-modal').modal('hide');
}

// Override message handling for this page
function handleNewMessage(data) {
    if (data.attendance_id == currentAttendanceId) {
        appendMessageToChat(data);
        scrollChatToBottom();
        
        // Play sound for incoming messages
        if (data.direction === 'incoming') {
            playNotificationSound();
        }
    }
}

// Custom message sending for this page
function sendMessage() {
    const content = $('#message-content').val().trim();
    const mediaUrl = $('#media-url').val();
    
    if (!content && !mediaUrl) {
        showToast('Digite uma mensagem', 'warning');
        return;
    }
    
    const messageData = {
        attendance_id: currentAttendanceId,
        content: content,
        media_type: mediaUrl ? 'file' : 'text',
        media_url: mediaUrl
    };
    
    // Show loading state
    $('#send-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');
    
    $.ajax({
        url: '/chat/send_message',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(messageData),
        success: function(response) {
            if (response.success) {
                $('#message-content').val('');
                $('#media-url').val('');
                
                // Message will be added via WebSocket
            } else {
                showToast('Erro ao enviar mensagem', 'error');
            }
        },
        error: function() {
            showToast('Erro ao enviar mensagem', 'error');
        },
        complete: function() {
            $('#send-btn').prop('disabled', false).html('<i class="fas fa-paper-plane"></i>');
            $('#message-content').focus();
        }
    });
}
</script>
{% endblock %}
