{% extends "base.html" %}

{% block title %}Alterar <PERSON> - Multi Atendimento{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-key"></i> Alterar <PERSON></h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.current_password.label(class="form-label") }}
                        {{ form.current_password(class="form-control" + (" is-invalid" if form.current_password.errors else "")) }}
                        {% if form.current_password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.current_password.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.new_password.label(class="form-label") }}
                        {{ form.new_password(class="form-control" + (" is-invalid" if form.new_password.errors else "")) }}
                        {% if form.new_password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.new_password.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">A senha deve ter pelo menos 6 caracteres.</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.confirm_password.label(class="form-label") }}
                        {{ form.confirm_password(class="form-control" + (" is-invalid" if form.confirm_password.errors else "")) }}
                        {% if form.confirm_password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.confirm_password.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Voltar
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-body">
                <h6><i class="fas fa-info-circle text-info"></i> Dicas de Segurança</h6>
                <ul class="list-unstyled small">
                    <li>• Use uma senha forte e única</li>
                    <li>• Combine letras maiúsculas, minúsculas e números</li>
                    <li>• Evite informações pessoais óbvias</li>
                    <li>• Altere sua senha regularmente</li>
                    <li>• Não compartilhe sua senha com outros</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
