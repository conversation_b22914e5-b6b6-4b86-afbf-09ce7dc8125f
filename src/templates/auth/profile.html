{% extends "base.html" %}

{% block title %}Meu Perfil - Multi Atendimento{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-user-circle"></i> Meu Perfil</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><strong>Nome Completo:</strong></label>
                            <p class="form-control-plaintext">{{ user.name }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><strong>Nome de Usuário:</strong></label>
                            <p class="form-control-plaintext">{{ user.username }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><strong>Email:</strong></label>
                            <p class="form-control-plaintext">{{ user.email }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><strong>Função:</strong></label>
                            <p class="form-control-plaintext">
                                {% if user.role == 'admin' %}
                                    <span class="badge bg-danger">Administrador</span>
                                {% elif user.role == 'supervisor' %}
                                    <span class="badge bg-warning">Supervisor</span>
                                {% else %}
                                    <span class="badge bg-info">Atendente</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><strong>Membro desde:</strong></label>
                            <p class="form-control-plaintext">{{ user.created_at.strftime('%d/%m/%Y') }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><strong>Último acesso:</strong></label>
                            <p class="form-control-plaintext">
                                {% if user.last_login %}
                                    {{ user.last_login.strftime('%d/%m/%Y às %H:%M') }}
                                {% else %}
                                    Primeiro acesso
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex gap-2">
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-primary">
                        <i class="fas fa-key"></i> Alterar Senha
                    </a>
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Voltar ao Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> Minhas Estatísticas</h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mb-3">
                        <h3 class="text-primary">{{ user.attendances|length }}</h3>
                        <small class="text-muted">Total de Atendimentos</small>
                    </div>
                    
                    <div class="mb-3">
                        <h3 class="text-success">
                            {{ user.attendances|selectattr('status', 'equalto', 'closed')|list|length }}
                        </h3>
                        <small class="text-muted">Atendimentos Finalizados</small>
                    </div>
                    
                    <div class="mb-3">
                        <h3 class="text-warning">
                            {{ user.attendances|selectattr('status', 'equalto', 'open')|list|length }}
                        </h3>
                        <small class="text-muted">Atendimentos Ativos</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-shield-alt"></i> Segurança</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Conta ativa</span>
                </div>
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-lock text-primary me-2"></i>
                    <span>Senha protegida</span>
                </div>
                <div class="d-flex align-items-center">
                    <i class="fas fa-user-shield text-info me-2"></i>
                    <span>Perfil verificado</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
