{% extends "base.html" %}

{% block title %}Login - Multi Atendimento{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-comments fa-3x text-primary"></i>
                    <h3 class="mt-3">Multi Atendimento</h3>
                    <p class="text-muted">Faça login para continuar</p>
                </div>
                
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.username.label(class="form-label") }}
                        {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                        {% if form.username.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.username.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3 form-check">
                        {{ form.remember_me(class="form-check-input") }}
                        {{ form.remember_me.label(class="form-check-label") }}
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
        
        <div class="text-center mt-3">
            <small class="text-muted">
                Sistema de Multi Atendimento WhatsApp<br>
                Versão 1.0
            </small>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // Focar no campo de usuário
    $('#username').focus();
    
    // Adicionar animação de loading no botão de submit
    $('form').on('submit', function() {
        var btn = $(this).find('button[type="submit"]');
        btn.html('<i class="fas fa-spinner fa-spin"></i> Entrando...');
        btn.prop('disabled', true);
    });
});
</script>
{% endblock %}
