{% extends "base.html" %}

{% block title %}Registrar Operador - Multi Atendimento{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-user-plus"></i> Registrar Novo Operador</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.name.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.username.label(class="form-label") }}
                                {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                                {% if form.username.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.username.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.email.label(class="form-label") }}
                                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                {% if form.email.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.email.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.role.label(class="form-label") }}
                                {{ form.role(class="form-select" + (" is-invalid" if form.role.errors else "")) }}
                                {% if form.role.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.role.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.password.label(class="form-label") }}
                                {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                                {% if form.password.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.password.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.password2.label(class="form-label") }}
                                {{ form.password2(class="form-control" + (" is-invalid" if form.password2.errors else "")) }}
                                {% if form.password2.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.password2.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('operators.list_operators') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Voltar
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Informações</h5>
            </div>
            <div class="card-body">
                <h6>Funções do Sistema:</h6>
                <ul class="list-unstyled">
                    <li><strong>Administrador:</strong> Acesso total ao sistema</li>
                    <li><strong>Supervisor:</strong> Gerencia operadores e atendimentos</li>
                    <li><strong>Atendente:</strong> Realiza atendimentos</li>
                </ul>
                
                <hr>
                
                <h6>Requisitos de Senha:</h6>
                <ul class="list-unstyled small">
                    <li>• Mínimo de 6 caracteres</li>
                    <li>• Recomendado usar letras e números</li>
                    <li>• Evitar senhas óbvias</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
