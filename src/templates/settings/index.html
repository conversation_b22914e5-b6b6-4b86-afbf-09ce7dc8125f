{% extends "base.html" %}

{% block title %}Configurações - Multi Atendimento{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-cog"></i> Configurações do Sistema</h2>
</div>

<div class="row">
    <!-- <PERSON><PERSON> Configurações -->
    <div class="col-md-3">
        <div class="list-group">
            <a href="{{ url_for('settings.system_settings') }}"
               class="list-group-item list-group-item-action">
                <i class="fas fa-server"></i> Sistema
            </a>
            <a href="{{ url_for('settings.whatsapp_settings') }}"
               class="list-group-item list-group-item-action">
                <i class="fab fa-whatsapp"></i> WhatsApp
            </a>
            <a href="{{ url_for('settings.notification_settings') }}"
               class="list-group-item list-group-item-action">
                <i class="fas fa-bell"></i> Notificações
            </a>
            <a href="{{ url_for('settings.backup_settings') }}"
               class="list-group-item list-group-item-action">
                <i class="fas fa-database"></i> Backup
            </a>
        </div>
    </div>

    <!-- Conteúdo Principal -->
    <div class="col-md-9">
        <!-- Status do Sistema -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Status do Sistema</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-check-circle text-success fa-2x me-3"></i>
                            <div>
                                <h6 class="mb-0">Sistema Online</h6>
                                <small class="text-muted">Funcionando normalmente</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-database text-info fa-2x me-3"></i>
                            <div>
                                <h6 class="mb-0">Banco de Dados</h6>
                                <small class="text-muted">Conectado</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <i id="whatsapp-status-icon" class="fab fa-whatsapp text-warning fa-2x me-3"></i>
                            <div>
                                <h6 class="mb-0">WhatsApp API</h6>
                                <small id="whatsapp-status-text" class="text-muted">Verificando...</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-wifi text-success fa-2x me-3"></i>
                            <div>
                                <h6 class="mb-0">WebSocket</h6>
                                <small class="text-muted">Conectado</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-3">
                    <button class="btn btn-outline-primary" onclick="checkSystemStatus()">
                        <i class="fas fa-sync-alt"></i> Verificar Status
                    </button>
                </div>
            </div>
        </div>

        <!-- Configurações Rápidas -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> Configurações Rápidas</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-server fa-3x text-primary mb-3"></i>
                                <h6>Configurações do Sistema</h6>
                                <p class="text-muted small">Nome, empresa, limites e timeouts</p>
                                <a href="{{ url_for('settings.system_settings') }}" class="btn btn-primary btn-sm">
                                    Configurar
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fab fa-whatsapp fa-3x text-success mb-3"></i>
                                <h6>WhatsApp / WAHA</h6>
                                <p class="text-muted small">API, sessões e webhooks</p>
                                <a href="{{ url_for('settings.whatsapp_settings') }}" class="btn btn-success btn-sm">
                                    Configurar
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-bell fa-3x text-warning mb-3"></i>
                                <h6>Notificações</h6>
                                <p class="text-muted small">Email, browser e sons</p>
                                <a href="{{ url_for('settings.notification_settings') }}" class="btn btn-warning btn-sm">
                                    Configurar
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-database fa-3x text-info mb-3"></i>
                                <h6>Backup</h6>
                                <p class="text-muted small">Backup automático e restauração</p>
                                <a href="{{ url_for('settings.backup_settings') }}" class="btn btn-info btn-sm">
                                    Configurar
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informações do Sistema -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info"></i> Informações do Sistema</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Versão:</strong></td>
                                <td>1.0.0</td>
                            </tr>
                            <tr>
                                <td><strong>Python:</strong></td>
                                <td>3.11+</td>
                            </tr>
                            <tr>
                                <td><strong>Flask:</strong></td>
                                <td>3.1.1</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Operadores:</strong></td>
                                <td id="operator-count">-</td>
                            </tr>
                            <tr>
                                <td><strong>Atendimentos:</strong></td>
                                <td id="attendance-count">-</td>
                            </tr>
                            <tr>
                                <td><strong>Mensagens:</strong></td>
                                <td id="message-count">-</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Check system status on load
$(document).ready(function() {
    checkWhatsAppStatus();
    loadSystemStats();
});

function checkSystemStatus() {
    checkWhatsAppStatus();
    loadSystemStats();
    showToast('Status verificado', 'info');
}

function checkWhatsAppStatus() {
    $.ajax({
        url: '{{ url_for("settings.test_whatsapp_connection") }}',
        method: 'POST',
        success: function(response) {
            if (response.success) {
                $('#whatsapp-status-icon').removeClass('text-warning text-danger').addClass('text-success');
                $('#whatsapp-status-text').text('Conectado');
            } else {
                $('#whatsapp-status-icon').removeClass('text-warning text-success').addClass('text-danger');
                $('#whatsapp-status-text').text('Erro de conexão');
            }
        },
        error: function() {
            $('#whatsapp-status-icon').removeClass('text-warning text-success').addClass('text-danger');
            $('#whatsapp-status-text').text('Não conectado');
        }
    });
}

function loadSystemStats() {
    // Simular carregamento de estatísticas
    // Em uma implementação real, isso viria de uma API
    $('#operator-count').text('3');
    $('#attendance-count').text('15');
    $('#message-count').text('127');
}
</script>
{% endblock %}
