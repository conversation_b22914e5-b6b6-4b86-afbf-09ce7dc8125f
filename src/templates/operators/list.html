{% extends "base.html" %}

{% block title %}Operadores - Multi Atendimento{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-users"></i> Operadores</h2>
    <div>
        <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
            <i class="fas fa-user-plus"></i> Novo Operador
        </a>
        <button class="btn btn-outline-secondary" onclick="location.reload()">
            <i class="fas fa-sync-alt"></i> Atualizar
        </button>
    </div>
</div>

<!-- Filtros -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Buscar:</label>
                <input type="text" name="search" class="form-control" 
                       value="{{ search or '' }}" placeholder="Nome, usuário ou email">
            </div>
            <div class="col-md-3">
                <label class="form-label">Função:</label>
                <select name="role" class="form-select">
                    <option value="">Todas as funções</option>
                    <option value="admin" {{ 'selected' if role_filter == 'admin' }}>Administrador</option>
                    <option value="supervisor" {{ 'selected' if role_filter == 'supervisor' }}>Supervisor</option>
                    <option value="attendant" {{ 'selected' if role_filter == 'attendant' }}>Atendente</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Status:</label>
                <select name="status" class="form-select">
                    <option value="">Todos</option>
                    <option value="active" {{ 'selected' if status_filter == 'active' }}>Ativos</option>
                    <option value="inactive" {{ 'selected' if status_filter == 'inactive' }}>Inativos</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> Filtrar
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Lista de Operadores -->
<div class="row">
    {% for operator in operators %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5 class="card-title mb-1">{{ operator.name }}</h5>
                        <p class="text-muted mb-0">@{{ operator.username }}</p>
                    </div>
                    <div>
                        {% if operator.is_active %}
                            <span class="badge bg-success">Ativo</span>
                        {% else %}
                            <span class="badge bg-secondary">Inativo</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-envelope"></i> {{ operator.email }}
                    </small><br>
                    <small class="text-muted">
                        <i class="fas fa-user-tag"></i> 
                        {% if operator.role == 'admin' %}
                            <span class="text-danger">Administrador</span>
                        {% elif operator.role == 'supervisor' %}
                            <span class="text-warning">Supervisor</span>
                        {% else %}
                            <span class="text-info">Atendente</span>
                        {% endif %}
                    </small><br>
                    <small class="text-muted">
                        <i class="fas fa-calendar"></i> 
                        Desde {{ operator.created_at.strftime('%d/%m/%Y') }}
                    </small>
                </div>
                
                <!-- Estatísticas do Operador -->
                {% if operator.id in operator_stats %}
                <div class="row text-center mb-3">
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="text-primary mb-0">{{ operator_stats[operator.id].total_attendances }}</h6>
                            <small class="text-muted">Atendimentos</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="text-success mb-0">{{ operator_stats[operator.id].closed_attendances }}</h6>
                            <small class="text-muted">Finalizados</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h6 class="text-info mb-0">{{ operator_stats[operator.id].efficiency }}%</h6>
                        <small class="text-muted">Eficiência</small>
                    </div>
                </div>
                {% endif %}
                
                <div class="d-flex gap-2">
                    <a href="{{ url_for('operators.operator_detail', operator_id=operator.id) }}" 
                       class="btn btn-sm btn-outline-primary flex-fill">
                        <i class="fas fa-eye"></i> Ver
                    </a>
                    {% if current_user.role == 'admin' %}
                    <a href="{{ url_for('operators.edit_operator', operator_id=operator.id) }}" 
                       class="btn btn-sm btn-outline-warning">
                        <i class="fas fa-edit"></i>
                    </a>
                    <button class="btn btn-sm btn-outline-{{ 'danger' if operator.is_active else 'success' }} toggle-status-btn"
                            data-operator-id="{{ operator.id }}" 
                            data-current-status="{{ operator.is_active|lower }}"
                            {{ 'disabled' if operator.id == current_user.id }}>
                        <i class="fas fa-{{ 'ban' if operator.is_active else 'check' }}"></i>
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% if not operators %}
<div class="text-center py-5">
    <i class="fas fa-users fa-3x text-muted mb-3"></i>
    <h4 class="text-muted">Nenhum operador encontrado</h4>
    <p class="text-muted">Ajuste os filtros ou adicione novos operadores</p>
    <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
        <i class="fas fa-user-plus"></i> Adicionar Primeiro Operador
    </a>
</div>
{% endif %}

<!-- Estatísticas Gerais -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary">{{ operators|length }}</h4>
                <small class="text-muted">Total de Operadores</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success">{{ operators|selectattr('is_active')|list|length }}</h4>
                <small class="text-muted">Ativos</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning">{{ operators|selectattr('role', 'equalto', 'supervisor')|list|length }}</h4>
                <small class="text-muted">Supervisores</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info">{{ operators|selectattr('role', 'equalto', 'attendant')|list|length }}</h4>
                <small class="text-muted">Atendentes</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Handle status toggle
$(document).on('click', '.toggle-status-btn', function() {
    const btn = $(this);
    const operatorId = btn.data('operator-id');
    const currentStatus = btn.data('current-status') === 'true';
    const action = currentStatus ? 'desativar' : 'ativar';
    
    if (confirm(`Tem certeza que deseja ${action} este operador?`)) {
        btn.prop('disabled', true);
        
        $.ajax({
            url: `/operators/${operatorId}/toggle-status`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    showToast(response.message, 'success');
                    
                    // Update button and badge
                    const newStatus = response.is_active;
                    btn.data('current-status', newStatus);
                    
                    if (newStatus) {
                        btn.removeClass('btn-outline-success').addClass('btn-outline-danger');
                        btn.html('<i class="fas fa-ban"></i>');
                        btn.closest('.card').find('.badge').removeClass('bg-secondary').addClass('bg-success').text('Ativo');
                    } else {
                        btn.removeClass('btn-outline-danger').addClass('btn-outline-success');
                        btn.html('<i class="fas fa-check"></i>');
                        btn.closest('.card').find('.badge').removeClass('bg-success').addClass('bg-secondary').text('Inativo');
                    }
                } else {
                    showToast(response.message, 'error');
                }
            },
            error: function() {
                showToast('Erro ao alterar status do operador', 'error');
            },
            complete: function() {
                btn.prop('disabled', false);
            }
        });
    }
});

// Auto-submit form on filter change
$('select[name="role"], select[name="status"]').on('change', function() {
    $(this).closest('form').submit();
});

// Clear filters
function clearFilters() {
    window.location.href = '{{ url_for("operators.list_operators") }}';
}
</script>
{% endblock %}
