{% extends "base.html" %}

{% block title %}Dashboard - Multi Atendimento{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tachometer-alt"></i> Dashboard</h2>
    <div>
        <span class="text-muted">Bem-vindo, {{ user.name }}!</span>
        <button class="btn btn-outline-primary btn-sm ms-2" onclick="refreshStats()">
            <i class="fas fa-sync-alt"></i> Atualizar
        </button>
    </div>
</div>

<!-- Estatísticas Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.today_attendances }}</h4>
                        <p class="card-text">Atendimentos Hoje</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.today_closed }}</h4>
                        <p class="card-text">Finalizados Hoje</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.pending }}</h4>
                        <p class="card-text">Pendentes</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.in_progress }}</h4>
                        <p class="card-text">Em Andamento</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-comments fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Métricas Adicionais -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">Eficiência</h5>
                <h3 class="text-primary">{{ stats.efficiency }}%</h3>
                <small class="text-muted">Taxa de finalização hoje</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">Tempo Médio</h5>
                <h3 class="text-success">{{ stats.avg_duration }} min</h3>
                <small class="text-muted">Duração média dos atendimentos</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">Mensagens</h5>
                <h3 class="text-info">{{ stats.today_messages }}</h3>
                <small class="text-muted">Mensagens enviadas hoje</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Atendimentos Pendentes -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-clock text-warning"></i> Atendimentos Pendentes</h5>
                <a href="{{ url_for('chat.index') }}" class="btn btn-sm btn-outline-primary">Ver Todos</a>
            </div>
            <div class="card-body">
                {% if pending_attendances %}
                    <div class="list-group list-group-flush">
                        {% for attendance in pending_attendances %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ attendance.contact.name or attendance.contact.phone }}</h6>
                                <p class="mb-1 text-muted">{{ attendance.subject }}</p>
                                <small class="text-muted">
                                    {{ attendance.created_at.strftime('%d/%m/%Y %H:%M') }}
                                </small>
                            </div>
                            <div>
                                {% if attendance.priority == 2 %}
                                    <span class="badge bg-danger">Urgente</span>
                                {% elif attendance.priority == 1 %}
                                    <span class="badge bg-warning">Alta</span>
                                {% else %}
                                    <span class="badge bg-secondary">Normal</span>
                                {% endif %}
                                <a href="{{ url_for('chat.attendance_detail', attendance_id=attendance.id) }}" 
                                   class="btn btn-sm btn-primary ms-2">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <p>Nenhum atendimento pendente!</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Atendimentos Recentes -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-history text-info"></i> Atendimentos Recentes</h5>
                <a href="{{ url_for('dashboard.stats') }}" class="btn btn-sm btn-outline-primary">Estatísticas</a>
            </div>
            <div class="card-body">
                {% if recent_attendances %}
                    <div class="list-group list-group-flush">
                        {% for attendance in recent_attendances %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ attendance.contact.name or attendance.contact.phone }}</h6>
                                <p class="mb-1 text-muted">{{ attendance.subject }}</p>
                                <small class="text-muted">
                                    {{ attendance.updated_at.strftime('%d/%m/%Y %H:%M') }}
                                </small>
                            </div>
                            <div>
                                {% if attendance.status == 'closed' %}
                                    <span class="badge bg-success">Finalizado</span>
                                {% elif attendance.status == 'open' %}
                                    <span class="badge bg-primary">Aberto</span>
                                {% else %}
                                    <span class="badge bg-warning">Aguardando</span>
                                {% endif %}
                                <a href="{{ url_for('chat.attendance_detail', attendance_id=attendance.id) }}" 
                                   class="btn btn-sm btn-outline-primary ms-2">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-comments fa-3x mb-3"></i>
                        <p>Nenhum atendimento recente</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Ações Rápidas -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> Ações Rápidas</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ url_for('chat.index') }}" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-comments"></i><br>
                            <small>Atendimentos</small>
                        </a>
                    </div>
                    {% if user.role in ['admin', 'supervisor'] %}
                    <div class="col-md-3">
                        <a href="{{ url_for('operators.list_operators') }}" class="btn btn-outline-success w-100 mb-2">
                            <i class="fas fa-users"></i><br>
                            <small>Operadores</small>
                        </a>
                    </div>
                    {% endif %}
                    <div class="col-md-3">
                        <a href="{{ url_for('dashboard.stats') }}" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-chart-bar"></i><br>
                            <small>Relatórios</small>
                        </a>
                    </div>
                    {% if user.role == 'admin' %}
                    <div class="col-md-3">
                        <a href="{{ url_for('settings.index') }}" class="btn btn-outline-warning w-100 mb-2">
                            <i class="fas fa-cog"></i><br>
                            <small>Configurações</small>
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Conectar ao WebSocket para atualizações em tempo real
const socket = io();

socket.on('connect', function() {
    console.log('Conectado ao servidor WebSocket');
});

socket.on('new_attendance_notification', function(data) {
    // Atualizar contador de pendentes
    updatePendingCount();
    
    // Mostrar notificação
    showNotification('Novo atendimento recebido!', 'info');
});

socket.on('attendance_closed', function(data) {
    // Atualizar estatísticas
    refreshStats();
});

function refreshStats() {
    fetch('{{ url_for("dashboard.api_stats") }}')
        .then(response => response.json())
        .then(data => {
            // Atualizar cards de estatísticas
            updateStatsCards(data);
        })
        .catch(error => {
            console.error('Erro ao atualizar estatísticas:', error);
        });
}

function updateStatsCards(stats) {
    // Implementar atualização dos cards
    location.reload(); // Por enquanto, recarregar a página
}

function updatePendingCount() {
    // Atualizar contador de notificações
    const badge = document.getElementById('notification-count');
    const currentCount = parseInt(badge.textContent) || 0;
    badge.textContent = currentCount + 1;
    badge.style.display = 'block';
}

function showNotification(message, type = 'info') {
    // Criar notificação toast
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Remover após 5 segundos
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

// Atualizar estatísticas a cada 30 segundos
setInterval(refreshStats, 30000);
</script>
{% endblock %}
