"""
Integração com a API WAHA para interação com WhatsApp
"""
import requests
import json
import logging
from flask import current_app

logger = logging.getLogger(__name__)

class WahaAPI:
    """
    Cliente para integração com a API WAHA
    """
    
    def __init__(self, base_url=None, session_name="default"):
        """
        Inicializa o cliente da API WAHA
        
        Parâmetros:
        - base_url: URL base da API WAHA (ex: http://localhost:3000)
        - session_name: No<PERSON> da sessão WhatsApp a ser utilizada
        """
        self.base_url = base_url
        self.session_name = session_name
        
    def _get_base_url(self):
        """Retorna a URL base da API, priorizando a configurada na instância ou a do app"""
        if self.base_url:
            return self.base_url
        return current_app.config.get('WAHA_API_URL', 'http://localhost:3000')
    
    def _make_request(self, method, endpoint, data=None, params=None, files=None):
        """
        Realiza uma requisição para a API WAHA
        
        Parâmetros:
        - method: <PERSON>étodo <PERSON>TTP (GET, POST, PUT, DELETE)
        - endpoint: Endpoint da API
        - data: Dados para enviar no corpo da requisição
        - params: Parâmetros de query string
        - files: Arquivos para upload
        
        Retorna:
        - Resposta da API em formato JSON
        """
        url = f"{self._get_base_url()}{endpoint}"
        
        headers = {
            'Accept': 'application/json'
        }
        
        if data and not files:
            headers['Content-Type'] = 'application/json'
            data = json.dumps(data)
        
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                data=data,
                params=params,
                files=files
            )
            
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro na requisição para a API WAHA: {str(e)}")
            if hasattr(e, 'response') and e.response:
                logger.error(f"Resposta de erro: {e.response.text}")
            raise
    
    # Métodos para gerenciamento de sessões
    
    def list_sessions(self):
        """Lista todas as sessões disponíveis"""
        return self._make_request('GET', '/api/sessions')
    
    def create_session(self, name=None):
        """
        Cria uma nova sessão
        
        Parâmetros:
        - name: Nome da sessão (opcional, usa o padrão da instância se não fornecido)
        """
        session_name = name or self.session_name
        data = {
            'name': session_name,
            'config': {
                'webhookUrl': current_app.config.get('WEBHOOK_URL'),
                'sessionId': session_name,
                'reconnectOnClose': True
            }
        }
        return self._make_request('POST', '/api/sessions', data=data)
    
    def get_session_info(self, name=None):
        """Obtém informações sobre uma sessão específica"""
        session_name = name or self.session_name
        return self._make_request('GET', f'/api/sessions/{session_name}')
    
    def start_session(self, name=None):
        """Inicia uma sessão"""
        session_name = name or self.session_name
        return self._make_request('POST', f'/api/sessions/{session_name}/start')
    
    def stop_session(self, name=None):
        """Para uma sessão"""
        session_name = name or self.session_name
        return self._make_request('POST', f'/api/sessions/{session_name}/stop')
    
    def get_qr_code(self, name=None):
        """Obtém o QR code para autenticação"""
        session_name = name or self.session_name
        return self._make_request('GET', f'/api/{session_name}/auth/qr')
    
    # Métodos para envio de mensagens
    
    def send_text(self, chat_id, text, reply_to=None, name=None):
        """
        Envia uma mensagem de texto
        
        Parâmetros:
        - chat_id: ID do chat (ex: <EMAIL>)
        - text: Texto da mensagem
        - reply_to: ID da mensagem a ser respondida (opcional)
        - name: Nome da sessão (opcional)
        """
        session_name = name or self.session_name
        data = {
            'chatId': chat_id,
            'text': text,
            'session': session_name
        }
        
        if reply_to:
            data['reply_to'] = reply_to
            
        return self._make_request('POST', '/api/sendText', data=data)
    
    def send_image(self, chat_id, image_url=None, caption=None, image_data=None, name=None):
        """
        Envia uma imagem
        
        Parâmetros:
        - chat_id: ID do chat
        - image_url: URL da imagem (alternativa a image_data)
        - caption: Legenda da imagem (opcional)
        - image_data: Dados binários da imagem (alternativa a image_url)
        - name: Nome da sessão (opcional)
        """
        session_name = name or self.session_name
        
        if image_url:
            data = {
                'chatId': chat_id,
                'url': image_url,
                'session': session_name
            }
            
            if caption:
                data['caption'] = caption
                
            return self._make_request('POST', '/api/sendImage', data=data)
        
        elif image_data:
            files = {
                'file': ('image.jpg', image_data, 'image/jpeg')
            }
            
            data = {
                'chatId': chat_id,
                'session': session_name
            }
            
            if caption:
                data['caption'] = caption
                
            return self._make_request('POST', '/api/sendImage', data=data, files=files)
        
        else:
            raise ValueError("É necessário fornecer image_url ou image_data")
    
    def send_file(self, chat_id, file_url=None, file_data=None, filename=None, name=None):
        """
        Envia um arquivo
        
        Parâmetros:
        - chat_id: ID do chat
        - file_url: URL do arquivo (alternativa a file_data)
        - file_data: Dados binários do arquivo (alternativa a file_url)
        - filename: Nome do arquivo (obrigatório se usar file_data)
        - name: Nome da sessão (opcional)
        """
        session_name = name or self.session_name
        
        if file_url:
            data = {
                'chatId': chat_id,
                'url': file_url,
                'session': session_name
            }
            
            return self._make_request('POST', '/api/sendFile', data=data)
        
        elif file_data and filename:
            files = {
                'file': (filename, file_data)
            }
            
            data = {
                'chatId': chat_id,
                'session': session_name
            }
            
            return self._make_request('POST', '/api/sendFile', data=data, files=files)
        
        else:
            raise ValueError("É necessário fornecer file_url ou (file_data e filename)")
    
    def send_voice(self, chat_id, voice_url=None, voice_data=None, name=None):
        """
        Envia uma mensagem de voz
        
        Parâmetros:
        - chat_id: ID do chat
        - voice_url: URL do áudio (alternativa a voice_data)
        - voice_data: Dados binários do áudio (alternativa a voice_url)
        - name: Nome da sessão (opcional)
        """
        session_name = name or self.session_name
        
        if voice_url:
            data = {
                'chatId': chat_id,
                'url': voice_url,
                'session': session_name
            }
            
            return self._make_request('POST', '/api/sendVoice', data=data)
        
        elif voice_data:
            files = {
                'file': ('audio.ogg', voice_data, 'audio/ogg')
            }
            
            data = {
                'chatId': chat_id,
                'session': session_name
            }
            
            return self._make_request('POST', '/api/sendVoice', data=data, files=files)
        
        else:
            raise ValueError("É necessário fornecer voice_url ou voice_data")
    
    def send_buttons(self, chat_id, title, buttons, footer=None, name=None):
        """
        Envia mensagem com botões interativos
        
        Parâmetros:
        - chat_id: ID do chat
        - title: Título da mensagem
        - buttons: Lista de botões no formato [{"id": "btn1", "text": "Botão 1"}, ...]
        - footer: Texto de rodapé (opcional)
        - name: Nome da sessão (opcional)
        """
        session_name = name or self.session_name
        
        data = {
            'chatId': chat_id,
            'title': title,
            'buttons': buttons,
            'session': session_name
        }
        
        if footer:
            data['footer'] = footer
            
        return self._make_request('POST', '/api/sendButtons', data=data)
    
    # Métodos para obtenção de mensagens e status
    
    def get_messages(self, chat_id, limit=100, name=None):
        """
        Obtém mensagens de um chat
        
        Parâmetros:
        - chat_id: ID do chat
        - limit: Limite de mensagens a retornar
        - name: Nome da sessão (opcional)
        """
        session_name = name or self.session_name
        
        params = {
            'chatId': chat_id,
            'limit': limit,
            'session': session_name
        }
        
        return self._make_request('GET', '/api/messages', params=params)
    
    def check_number_status(self, phone, name=None):
        """
        Verifica se um número está registrado no WhatsApp
        
        Parâmetros:
        - phone: Número de telefone (sem o +)
        - name: Nome da sessão (opcional)
        """
        session_name = name or self.session_name
        
        params = {
            'phone': phone,
            'session': session_name
        }
        
        return self._make_request('GET', '/api/checkNumberStatus', params=params)
    
    def send_seen(self, chat_id, name=None):
        """
        Marca mensagens como vistas
        
        Parâmetros:
        - chat_id: ID do chat
        - name: Nome da sessão (opcional)
        """
        session_name = name or self.session_name
        
        data = {
            'chatId': chat_id,
            'session': session_name
        }
        
        return self._make_request('POST', '/api/sendSeen', data=data)
    
    def start_typing(self, chat_id, duration=3000, name=None):
        """
        Inicia indicador de digitação
        
        Parâmetros:
        - chat_id: ID do chat
        - duration: Duração em milissegundos
        - name: Nome da sessão (opcional)
        """
        session_name = name or self.session_name
        
        data = {
            'chatId': chat_id,
            'duration': duration,
            'session': session_name
        }
        
        return self._make_request('POST', '/api/startTyping', data=data)
    
    def stop_typing(self, chat_id, name=None):
        """
        Para indicador de digitação
        
        Parâmetros:
        - chat_id: ID do chat
        - name: Nome da sessão (opcional)
        """
        session_name = name or self.session_name
        
        data = {
            'chatId': chat_id,
            'session': session_name
        }
        
        return self._make_request('POST', '/api/stopTyping', data=data)

# Função para inicializar a integração com a aplicação Flask
def init_app(app):
    """
    Inicializa a integração da API WAHA com a aplicação Flask
    """
    app.config.setdefault('WAHA_API_URL', 'http://localhost:3000')
    app.config.setdefault('WAHA_SESSION_NAME', 'default')
    
    # Cria uma instância da API e a disponibiliza no contexto da aplicação
    app.waha_api = WahaAPI(
        base_url=app.config['WAHA_API_URL'],
        session_name=app.config['WAHA_SESSION_NAME']
    )
