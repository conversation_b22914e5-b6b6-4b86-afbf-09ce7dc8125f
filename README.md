# Sistema de Multi Atendimento com Flask e API Waha

Este sistema permite gerenciar múltiplos atendimentos via WhatsApp utilizando Flask e a API Waha, com suporte a múltiplos operadores, filas de atendimento e comunicação em tempo real.

## Requisitos

- Python 3.8+
- Docker (para executar a API Waha)
- Ambiente virtual Python (venv)

## Instalação

### 1. Clone o repositório

```bash
git clone <url-do-repositorio>
cd multi_atendimento_waha
```

### 2. Configure o ambiente virtual

```bash
python -m venv venv
source venv/bin/activate  # No Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 3. Configure as variáveis de ambiente

Crie um arquivo `.env` na raiz do projeto com as seguintes variáveis:

```
SECRET_KEY=sua_chave_secreta_aqui
DATABASE_URL=sqlite:///multi_atendimento.db
WAHA_API_URL=http://localhost:3000
WAHA_SESSION_NAME=default
WEBHOOK_URL=https://seu-dominio.com/webhook/waha
```

Para desenvolvimento local, você pode usar ngrok para expor seu webhook:
```bash
ngrok http 5000
```

E então atualizar a variável `WEBHOOK_URL` com a URL fornecida pelo ngrok.

### 4. Inicie a API Waha

```bash
docker run -it --rm -p 3000:3000/tcp --name waha devlikeapro/waha
```

Acesse o Dashboard da Waha em http://localhost:3000/dashboard para iniciar uma sessão e escanear o QR code.

### 5. Inicialize o banco de dados

```bash
python init_db.py
```

Este comando criará as tabelas do banco de dados e usuários de exemplo:
- **admin** / admin123 (Administrador)
- **supervisor** / supervisor123 (Supervisor)
- **atendente** / atendente123 (Atendente)

### 6. Execute o sistema

```bash
python main.py
```

O sistema estará disponível em http://localhost:5000

## Estrutura do Projeto

```
multi_atendimento_waha/
├── src/                    # Código fonte principal
│   ├── auth/               # Módulo de autenticação
│   ├── chat/               # Módulo de chat e atendimento
│   ├── dashboard/          # Módulo de dashboard
│   ├── models/             # Modelos de dados
│   ├── operators/          # Gerenciamento de operadores
│   ├── settings/           # Configurações do sistema
│   ├── static/             # Arquivos estáticos
│   ├── templates/          # Templates HTML
│   ├── waha_api/           # Integração com API Waha
│   ├── extensions/         # Extensões Flask
│   ├── app.py              # Factory pattern da aplicação
│   └── __init__.py
├── main.py                 # Ponto de entrada da aplicação
├── requirements.txt        # Dependências do projeto
└── todo.md                 # Checklist de requisitos
```

## Funcionalidades Principais

### Gerenciamento de Usuários
- Cadastro e login de operadores
- Perfis de acesso (admin, supervisor, atendente)

### Atendimento ao Cliente
- Fila de atendimentos
- Distribuição automática ou manual
- Transferência entre operadores
- Finalização e avaliação

### Chat em Tempo Real
- Comunicação via WebSockets
- Indicadores de digitação e leitura
- Suporte a diferentes tipos de mídia

### Integração com WhatsApp
- Envio e recebimento de mensagens
- Suporte a texto, imagem, áudio, vídeo, etc.
- Gerenciamento de sessões WhatsApp

### Dashboard e Relatórios
- Métricas de atendimento
- Tempo médio de resposta
- Volume de atendimentos

## Fluxo de Uso

1. Operador faz login no sistema
2. Sistema exibe fila de atendimentos pendentes
3. Operador aceita um atendimento
4. Interface de chat é aberta para comunicação
5. Mensagens são trocadas em tempo real
6. Operador pode transferir ou finalizar o atendimento

## Integração com API Waha

O sistema se integra com a API Waha para:
- Gerenciar sessões WhatsApp
- Enviar e receber mensagens
- Monitorar status de mensagens
- Receber eventos via webhook

## Desenvolvimento Futuro

- Implementação de chatbots para atendimento inicial
- Integração com sistemas de CRM
- Análise de sentimento nas mensagens
- Suporte a múltiplas instâncias da API Waha
