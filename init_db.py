#!/usr/bin/env python3
"""
Script para inicializar o banco de dados com dados básicos
"""
import os
import sys
from datetime import datetime

# Adicionar o diretório raiz ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.app import create_app
from src.extensions.database import db
from src.models.user import User
from src.models.contact import Contact
from src.models.attendance import Attendance
from src.models.message import Message

def init_database():
    """Inicializa o banco de dados com dados básicos"""
    
    app = create_app()
    
    with app.app_context():
        print("Criando tabelas do banco de dados...")
        db.create_all()
        
        # Verificar se já existe um usuário admin
        admin_user = User.query.filter_by(role='admin').first()
        
        if not admin_user:
            print("Criando usuário administrador padrão...")
            
            # Criar usuário admin padrão
            admin = User(
                username='admin',
                email='<EMAIL>',
                name='Administrador',
                role='admin',
                is_active=True,
                created_at=datetime.utcnow()
            )
            admin.set_password('admin123')
            
            db.session.add(admin)
            
            # Criar usuário supervisor de exemplo
            supervisor = User(
                username='supervisor',
                email='<EMAIL>',
                name='Supervisor Exemplo',
                role='supervisor',
                is_active=True,
                created_at=datetime.utcnow()
            )
            supervisor.set_password('supervisor123')
            
            db.session.add(supervisor)
            
            # Criar usuário atendente de exemplo
            attendant = User(
                username='atendente',
                email='<EMAIL>',
                name='Atendente Exemplo',
                role='attendant',
                is_active=True,
                created_at=datetime.utcnow()
            )
            attendant.set_password('atendente123')
            
            db.session.add(attendant)
            
            db.session.commit()
            
            print("Usuários criados com sucesso!")
            print("\nCredenciais de acesso:")
            print("=" * 50)
            print("Administrador:")
            print("  Usuário: admin")
            print("  Senha: admin123")
            print("\nSupervisor:")
            print("  Usuário: supervisor")
            print("  Senha: supervisor123")
            print("\nAtendente:")
            print("  Usuário: atendente")
            print("  Senha: atendente123")
            print("=" * 50)
            
        else:
            print("Usuário administrador já existe.")
        
        # Criar contatos de exemplo
        if Contact.query.count() == 0:
            print("Criando contatos de exemplo...")
            
            contacts = [
                Contact(
                    phone='5511999999999',
                    name='João Silva',
                    whatsapp_id='<EMAIL>',
                    created_at=datetime.utcnow()
                ),
                Contact(
                    phone='5511888888888',
                    name='Maria Santos',
                    whatsapp_id='<EMAIL>',
                    created_at=datetime.utcnow()
                ),
                Contact(
                    phone='5511777777777',
                    name='Pedro Oliveira',
                    whatsapp_id='<EMAIL>',
                    created_at=datetime.utcnow()
                )
            ]
            
            for contact in contacts:
                db.session.add(contact)
            
            db.session.commit()
            print("Contatos de exemplo criados!")
        
        # Criar atendimentos de exemplo
        if Attendance.query.count() == 0:
            print("Criando atendimentos de exemplo...")
            
            contacts = Contact.query.all()
            users = User.query.filter(User.role.in_(['attendant', 'supervisor'])).all()
            
            if contacts and users:
                attendances = [
                    Attendance(
                        contact_id=contacts[0].id,
                        operator_id=users[0].id if users else None,
                        status='open',
                        priority=1,
                        subject='Dúvida sobre produto',
                        created_at=datetime.utcnow()
                    ),
                    Attendance(
                        contact_id=contacts[1].id,
                        status='waiting',
                        priority=0,
                        subject='Suporte técnico',
                        created_at=datetime.utcnow()
                    ),
                    Attendance(
                        contact_id=contacts[2].id,
                        operator_id=users[0].id if users else None,
                        status='closed',
                        priority=2,
                        subject='Reclamação urgente',
                        created_at=datetime.utcnow(),
                        closed_at=datetime.utcnow()
                    )
                ]
                
                for attendance in attendances:
                    db.session.add(attendance)
                
                db.session.commit()
                print("Atendimentos de exemplo criados!")
        
        print("\nBanco de dados inicializado com sucesso!")
        print("Você pode agora executar o sistema com: python main.py")

if __name__ == '__main__':
    init_database()
